/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { DefinitionMapItem } from '../service/reporting/types';

export interface ExternalMappingItem extends Pick<DefinitionMapItem, 'references'> {
  /** The code of the mapping, usually the factName **/
  mappingCode: string;
  /** standard type like gri, csrd, issb etc. **/
  type: string;
  name: string;
  utrCode: string;
  valueListCode?: string;
}
