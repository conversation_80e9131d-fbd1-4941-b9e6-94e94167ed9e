/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */



import { Blueprint } from '../repository/BlueprintRepository';
import { CompositeUtrConfigInterface, ConfigurationVariableSetup } from './compositeUtrConfigs';
import { UtrConfig } from '../rules/UtrConfig';

export const getMergedConfigs = (forms: Blueprint) => {
  return [...forms.additionalConfigs || [], ...forms.forms];
};

// Not dealing with value chain
export const extractUtrCodes = (blueprint: Blueprint): string[] => {

  const utrCodes = new Set<string>();
  const configs = getMergedConfigs(blueprint);

  for (const config of configs) {
    if (config.utrGroupConfig) {
      config.utrGroupConfig.utrCodes.forEach((c) => utrCodes.add(c));
    } else if (config.config) {
      const compositeConfig = <CompositeUtrConfigInterface>config.config;
      utrCodes.add(compositeConfig.compositeUtrCode);
      // This extracts the codes used in the formulas
      // NOTE: Under normal circumstances all codes should already be declared in utrGroupConfig,
      // or else it would never work on the formulas in the first place, but this makes validation
      // more robust to include both just in case
      UtrConfig.getFragmentUtrCodes(compositeConfig)
        .forEach(code => utrCodes.add(code));
    }
  }

  return Array.from(utrCodes);
};

export const extractVariables = (blueprint: Blueprint): Pick<ConfigurationVariableSetup, 'code' | 'valueListCode'>[] => {

  const variableMap = new Map<string, Pick<ConfigurationVariableSetup, 'code' | 'valueListCode'>>();
  const configs = getMergedConfigs(blueprint);
  for (const config of configs) {
    if (!config.config || !('importConfigurationData' in config.config)) {
      continue;
    }

    Object.values(config.config.importConfigurationData.variables)
      .forEach(variable => {
        const uniqKey = `${variable.code}::${variable.valueListCode ?? ''}`;
        variableMap.set(uniqKey, variable);
      });
  }

  return Array.from(variableMap.values());
}

export const extractVisibleUtrCodes = (blueprint: Pick<Blueprint, 'forms'>): string[] => {
  const utrCodes = new Set<string>();
  for (const config of blueprint.forms) {
    if (config.utrGroupConfig) {
      config.utrGroupConfig.utrCodes.forEach((c) => utrCodes.add(c));
    }
  }

  return Array.from(utrCodes);
};

