/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UserPlain } from '../../models/user';
import UniversalTrackerValue, { UniversalTrackerValueExtended } from '../../models/universalTrackerValue';
import {
  convertRecord,
  CsvColumnSetup,
  getCsvHeaders,
  CsvPropertyAccessor,
} from '../assurance/csvContext';
import { BundleContext } from '../assurance/UtrvFileBundler';
import { customDateFormat, DateFormat } from '../../util/date';
import sanitize from 'sanitize-filename';
import { ObjectId } from 'bson';
import { BundleData } from "../../repository/UniversalTrackerRepository";
import { ValueList } from '../../models/public/valueList';
import { ValueListRepository } from '../../repository/ValueListRepository';
import { extractListId } from '../utr/utrUtil';
import { simpleUtrValueTypes } from '../../models/public/universalTrackerType';
import { universalTrackerLookup } from '../../repository/utrvAggregations';
import { universalTrackerValuePlainFields } from '../../repository/projections';
import { universalTrackerPublicFields } from '../../models/public/projectionUtils';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { PipelineStage } from 'mongoose';
import { QUESTION } from '../../util/terminology';

interface FileDownload {
  header: string[],
  records: unknown[][],
  filename: string;
}

const requiredFields = [
  'Id',
  `${QUESTION.CAPITALIZED_SINGULAR} Type`,
  QUESTION.CAPITALIZED_SINGULAR,
  'Input',
  'Status',
  'Unit',
];
const importHeaders = getCsvHeaders({ displayUserInput: true }).filter((h) => requiredFields.includes(h.name));

interface CreateOptions {
  columns?: CsvColumnSetup[];
  name?: string;
  utrvs: UniversalTrackerValueExtended[];
  valueLists: ValueList[];
}

interface CsvExportRequest {
  utrvIds: ObjectId[];
  user: UserPlain
  filename?: string;
  date?: Date;
  columnSetup?: CsvColumnSetup[],
  includeComplexType?: boolean;
  includeValueList?: boolean;
}

export class SurveyDataTransfer {

  public async createCsvForUtrvs(exportRequest: CsvExportRequest): Promise<FileDownload> {
    const {
      utrvIds,
      user,
      filename,
      date = new Date(),
      columnSetup = importHeaders,
      includeComplexType = true,
      includeValueList,
    } = exportRequest;

    const aggregate: PipelineStage[] = [];

    if (includeComplexType) {
      aggregate.push({
        $match: {
          _id: {
            $in: utrvIds
          }
        }
      })
    } else {
      aggregate.push({
        $match: {
          _id: {
            $in: utrvIds
          },
          $or: [
            {
              valueType: {
                $exists: false
              }
            },
            {
              valueType: {
                $in: simpleUtrValueTypes
              }
            }
          ]
        }
      })
    }

    aggregate.push(universalTrackerLookup);
    aggregate.push({
      $project: {
        ...universalTrackerValuePlainFields,
        universalTracker: {
          $arrayElemAt: ['$universalTracker', 0]
        },
      },
    });

    if (!includeComplexType) {
      aggregate.push({
        $match: {
          'universalTracker.valueType': { $in: simpleUtrValueTypes }
        }
      });
    }

    aggregate.push({
      $project: {
        ...universalTrackerValuePlainFields,
        universalTracker: universalTrackerPublicFields,
      },
    }
    );
    aggregate.push({ $sort: { effectiveDate: 1 } });

    const utrvs = <UniversalTrackerValueExtended[]>await UniversalTrackerValue
      .aggregate(aggregate)
      .exec();

    return this.createFrom({
      utrvs: utrvs.filter(utrv => !!utrv.universalTracker?._id),
      valueLists: await this.getValueList(utrvs, includeValueList),
      name: filename ?? `WWG-${customDateFormat(date, DateFormat.YearMonth)}-export.csv`,
      columns: columnSetup,
    }, user)
  }

  private async getValueList(utrvs: UniversalTrackerValueExtended[], includeValueList?: boolean) {
    if (!includeValueList) {
      return [];
    }
    const ids: ObjectId[] = [];
    utrvs.forEach(v => {
      ids.push(...extractListId(v.universalTracker.valueValidation));
    });
    return ValueListRepository.findByIds(ids);
  }

  private getRecords(
    utrvs: UniversalTrackerValueExtended[],
    user: UserPlain,
    valueLists: ValueList[],
    properties: CsvPropertyAccessor[]
  ) {
    const records: any[][] = [];

    utrvs.forEach((utrv) => {

      // Match existing structure
      const bundle: BundleContext = {
        assuranceEvidence: [],
        context: {
          ...utrv,
          universalTrackerValueAssurances: [],
          users: [],
        },
        contributions: {},
        preferredTypes: [],
        valueLists,
        user,
      }

      if (this.isMultiRowTable(bundle.context)) {
        this.createTableRecords(bundle, records, properties);
      } else {
        records.push(convertRecord(bundle, properties))
      }
    });
    return records;
  }

  public async createFrom(createOptions: CreateOptions, user: UserPlain): Promise<FileDownload> {

    const {
      columns = importHeaders,
      name = this.getDefaultFilename(),
      valueLists,
      utrvs
    } = createOptions;

    const header = columns.map(h => h.name)
    const properties = columns.map(h => h.property)
    const records = this.getRecords(utrvs, user, valueLists, properties);

    return { header, records, filename: sanitize(name) }
  }

  private getDefaultFilename() {
    return `WWG-${(customDateFormat(new Date(), DateFormat.YearMonth))}-export.csv`;
  }

  private createTableRecords(bundle: BundleContext, records: any[][], fieldProperties: CsvPropertyAccessor[]) {
    const tableData = bundle.context.valueData?.table ?? [];
    tableData.forEach((_, index) => {
      const record = convertRecord({
        ...bundle,
        tableDataIndex: index
      }, fieldProperties);
      records.push(record)
    })
  }

  private isMultiRowTable(utrvBundleData: BundleData) {
    if (utrvBundleData.universalTracker.valueType !== UtrValueType.Table) {
      return false;
    }

    const table = utrvBundleData.valueData?.table;
    return table && table?.length > 0;
  }
}

let instance: SurveyDataTransfer;
export const getSurveyDataTransfer = () => {
  if (!instance) {
    instance = new SurveyDataTransfer();
  }
  return instance;
}
