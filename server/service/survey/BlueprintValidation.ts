/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { Blueprints } from '../../survey/blueprints';
import {
  Blueprint,
  BlueprintRepositoryInterface,
  getBlueprintRepository, SurveyForm,
} from '../../repository/BlueprintRepository';
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import { CompositeUtrConfigInterface } from '../../survey/compositeUtrConfigs';

interface ErrorResponse {
  title: string;
  codes: string[];
}

export class BlueprintValidation {

  constructor(
    private blueprintRepo: BlueprintRepositoryInterface,
  ) {
  }

  public async validateBlueprint(code: Blueprints): Promise<ErrorResponse[]> {
    const blueprint = await this.blueprintRepo.mustFindExpandedByCode(code)
    return [
      ...this.validateUtrCodes(blueprint),
      ...this.validateFragmentConfiguration(blueprint),
    ];
  }

  public validateUtrCodes(blueprint: Blueprint): ErrorResponse[] {

    const codes = extractVisibleUtrCodes(blueprint);
    const mixCaseCodes = codes.filter(c => c !== c.toLowerCase())

    const errors: ErrorResponse[] = [];
    if (mixCaseCodes.length > 0) {
      const message = `Expected all utrGroupConfig codes to be in lowercase. Codes has mixed case: ${mixCaseCodes.join(',')}`;
      errors.push({ title: message, codes: mixCaseCodes })
    }

    const uniqueCodes = new Set<string>();
    const duplicateCodes: string[] = [];
    codes.forEach(code => {
      if (uniqueCodes.has(code)) {
        duplicateCodes.push(code)
      }
      uniqueCodes.add(code);
    })

    if (duplicateCodes.length > 0) {
      errors.push({
        title: `Found duplicate ${duplicateCodes.length} codes`,
        codes: duplicateCodes,
      });
    }

    return errors;
  }

  public validateFragmentConfiguration(blueprint: Blueprint) {

    const errors: ErrorResponse[] = [];

    const surveyForms: SurveyForm[] = [...blueprint.forms, ...blueprint.additionalConfigs];
    for (const form of surveyForms) {
      if (!form.compositeConfig) {
        continue;
      }

      const msg = `Failed to validate ${form.compositeConfig}`
      const compError: ErrorResponse = { title: msg, codes: [] };
      const {
        importConfigurationData,
        fragmentUtrCodes,
      } = <CompositeUtrConfigInterface>form.config;

      if (!fragmentUtrCodes) {
        continue;
      }

      Object.values(importConfigurationData.variables).forEach(({ code }) => {
        if (!fragmentUtrCodes.includes(code)) {
          compError.codes.push(code)
        }
      });

      if (compError.codes.length > 0) {
        errors.push(compError)
      }
    }

    return errors;
  }
}

export const getBlueprintValidation = () => {
  return new BlueprintValidation(
    getBlueprintRepository()
  );
}
