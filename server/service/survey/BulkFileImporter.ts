/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { DataImporter, getDataImporter } from './DataImporter';
import { SurveyModelPlain, SurveyType } from '../../models/survey';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { UserPlain } from '../../models/user';
import { DataTransform } from './transfer/DataTransform';
import { Excel, getExcel, Headers } from '../file/Excel';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import moment from 'moment';
import { WorkBook } from '@sheet/core/types';
import { InitiativePlain } from '../../models/initiative';
import { UniversalTrackerValueModel } from '../../models/universalTrackerValue';
import { FindParams, SurveyQuery } from './SurveyQuery';
import { DataPeriods } from '../utr/constants';
import { ObjectId } from "bson";
import { ActionRequest } from "../utr/model/actionRequest";
import { COLUMNS } from './transfer/ImportTemplateExcel';
import { SURVEY } from '../../util/terminology';
import UserError from "../../error/UserError";

interface InvalidRow {
  message: string;
  row: number;
  column: string;
}

export interface ResultErrors {
  /** Missing initiative codes */
  initiatives: { missing: string[] };

  /** Missing initiative code -> survey effective date */
  surveys: { missing: Record<string, string[]> };
  /**
   * Used to be Plain strings with more like developer errors, rather than user for example:
   *
   * "Empty initiative code column on row"
   * "Year column not found on row ${i + 2}"
   * "Invalid date on row ${i + 2}"
   * 
   * Now it is an object with more context including row and column.
   */
  invalidRows?: InvalidRow[];
}
interface ResultData {
  initiativeCode: string,
  surveyDate: string,
  surveyId: string,
  utrvs: Pick<UniversalTrackerValueModel, 'id' | 'value' | 'valueData' | 'valueType' | 'unit' | 'numberScale'>[]
}

interface SurveyDatesByInitiative {
  [key: string]: Set<string>
}

interface JsonRow {
  [key: string]: string | number
}
interface AggregatedJsonRows {
  // initiativeCode => YYYY-MM-DD => JsonRow[]
  [key: string]: Record<string, JsonRow[]>
}
interface SurveysLoadedByInitiative {
  [key: string]: Record<string, SurveyModelPlain>
}

const separator = '--';
const allowedPeriods = Object.values(DataPeriods) as string[];

export interface SurveyImportData {
  initiativeId: ObjectId;
  // Keeping it for backward compatability
  initiativeCode: string;
  surveyId: ObjectId;
  effectiveDate: string;
  updates: ActionRequest[]
}

interface SurveyUpdates {
  surveysLoaded: SurveysLoadedByInitiative;
  validRows: AggregatedJsonRows;
  user: UserPlain;
}

export interface ParseFileResult extends Omit<SurveyUpdates, 'user'> {
  errors: ResultErrors;
}

export class BulkFileImporter {

  constructor(
    private dataImporter: DataImporter,
    private logger: LoggerInterface,
    private excelParser: Excel,
  ) {
  }

  private tabNames = ['questions', 'import']
  private initiativeCodeColumns = ["Level Code"]
  private surveyDateColumns = {
    year: ['Survey Year', `${SURVEY.CAPITALIZED_SINGULAR} Year`, "Year"],
    month: ['Survey Month', `${SURVEY.CAPITALIZED_SINGULAR} Month`, "Month"],
    period: ["Duration", "Period"],
  }

  private getSheet(sheetNames: string[]) {
    return sheetNames.length === 1 ? sheetNames[0] : sheetNames.find(name => this.tabNames.includes(name.toLowerCase()))
  }

  private getRowEffectiveDate(month: string | number, year: string | number) {
    const intMonth = typeof month === 'string' ? parseInt(month) : month
    const intYear = typeof year === 'string' ? parseInt(year) : year
    let parsedDate: moment.Moment
    if (isNaN(intMonth)) {
      // using non-standard string format for string months e.g. 01 Jan 2022 , while for integers better setting it explicitly
      parsedDate = moment.utc(new Date(`01 ${month} ${year}`))
    } else {
      parsedDate = moment.utc({ month: intMonth - 1, year: intYear })
    }
    const date = parsedDate.endOf('day')
    if (!date.isValid()) {
      throw new UserError(`Invalid date, year: "${year}", month: ${month} `, { month, year, column: 'Month, Year' });
    }
    return date.toISOString()
  }

  private getRowEffectiveDateWithPeriod(month: string | number, year: string | number, row: JsonRow, headers: Headers) {
    const effectiveDate = this.getRowEffectiveDate(month, year);

    return `${effectiveDate}${separator}${this.getPeriod(row, headers)}`;
  }

  private getPeriod(row: JsonRow, headers: Headers) {
    const header = this.getColumnHeader(headers, this.surveyDateColumns.period);
    if (!header) {
      throw new UserError('Missing or empty', { column: 'Period' });
    }

    const value = typeof row[header] === 'string' ? row[header].toLowerCase() : row[header];
    const period = value === 'annually' ? DataPeriods.Yearly : value;

    if (!allowedPeriods.includes(period as string)) {
      throw new UserError(`Not permitted period: "${period}". Have to be one of: ${allowedPeriods.join(', ')}`, {
        value,
        column: header,
      });
    }

    return period;
  }

  private getColumnHeader(headers: Headers, columnNames: string[]) {
    return headers.find((colName) =>
      columnNames.some((w) => w.toLowerCase() === colName.toLowerCase())
    );
  }

  /**
   * aggregate rows by initiative and dates
   */
  public aggregateRows(data: JsonRow[], headers: string[]) {
    const surveysToLoad: SurveyDatesByInitiative = {}
    const aggregatedRows: AggregatedJsonRows = {}
    const rowsErrors: {
      message: string;
      column?: string;
      row?: number;
    }[] = [];
    data.forEach((row, i) => {
      const codeColumn = this.getColumnHeader(headers, this.initiativeCodeColumns);
      const rowNumber = i + 2; // current array index + start from 1 + add header row
      if (codeColumn === undefined || !row[codeColumn]) {
        rowsErrors.push({
          message: 'Missing or empty',
          row: rowNumber,
          column: 'Level code',
        });
        return;
      }

      const initiativeCode = row[codeColumn];

      if (!surveysToLoad[initiativeCode]) {
        surveysToLoad[initiativeCode] = new Set();
      }
      try {
        const yearColumn = this.getColumnHeader(headers, this.surveyDateColumns.year);
        const monthColumn = this.getColumnHeader(headers, this.surveyDateColumns.month);

        if (!yearColumn || !row[yearColumn]) {
          rowsErrors.push({ message: 'Missing or empty', row: rowNumber, column: 'Year' });
          return;
        }
        if (!monthColumn || !row[monthColumn]) {
          rowsErrors.push({ message: 'Missing or empty', row: rowNumber, column: 'Month' });
          return;
        }

        const hasPeriodColumn = !!this.getColumnHeader(headers, this.surveyDateColumns.period);
        const effectiveDate = hasPeriodColumn
          ? this.getRowEffectiveDateWithPeriod(row[monthColumn], row[yearColumn], row, headers)
          : this.getRowEffectiveDate(row[monthColumn], row[yearColumn]);

        surveysToLoad[initiativeCode].add(effectiveDate);

        if (!aggregatedRows[initiativeCode]) {
          aggregatedRows[initiativeCode] = {};
        }
        if (!aggregatedRows[initiativeCode][effectiveDate]) {
          aggregatedRows[initiativeCode][effectiveDate] = [];
        }
        aggregatedRows[initiativeCode][effectiveDate].push(row);
      } catch (e) {
        wwgLogger.error(e);

        if (e instanceof UserError) {
          rowsErrors.push({ message: e.message, row: rowNumber, column: typeof e.context?.column === 'string' ? e.context?.column : undefined });
          return;
        }

        rowsErrors.push({ message: 'Invalid date', row: rowNumber, column: 'Level code, Month, Year, Period' });
      }
    });
    return { aggregatedRows, surveysToLoad, rowsErrors }
  }

  /**
   * load surveys for the initiatives and dates found
   */
  private async loadSurveys(
    surveyDatesLookup: SurveyDatesByInitiative,
    initiativeTree: InitiativePlain[],
    sourceName: string,
    surveyType: SurveyType,
  ) {
    const loadErrors: ResultErrors = { initiatives: { missing: [] }, surveys: { missing: {} } }
    const surveysLoaded: SurveysLoadedByInitiative = {}
    for (const initiativeCode in surveyDatesLookup) {
      const foundInitiative = initiativeTree.find(el => el.code === initiativeCode)
      if (!foundInitiative) {
        loadErrors.initiatives.missing.push(initiativeCode)
        continue
      }
      const lookupDates = Array.from(surveyDatesLookup[initiativeCode])
      for (const effectiveDate of lookupDates) {
        const surveys = await this.getSurveys(
          { initiativeId: foundInitiative._id, effectiveDateWithPeriod: effectiveDate, sourceName, type: surveyType },
        );

        const [firstSurvey, secondSurvey] = surveys;
        if (!firstSurvey) {
          if (!loadErrors.surveys.missing[initiativeCode]) {
            loadErrors.surveys.missing[initiativeCode] = [];
          }
          loadErrors.surveys.missing[initiativeCode].push(effectiveDate)
          continue
        }

        if (secondSurvey) {
          throw new Error(`More than one survey found for ${initiativeCode} ${effectiveDate}`)
        }
        if (!surveysLoaded[initiativeCode]) {
          surveysLoaded[initiativeCode] = {}
        }
        surveysLoaded[initiativeCode][effectiveDate] = firstSurvey
      }
    }

    return { surveysLoaded, loadErrors }
  }

  private async getSurveys(
    {
      initiativeId,
      effectiveDateWithPeriod,
      sourceName,
      type,
    }: Omit<FindParams, 'effectiveDate' | 'period'> & { effectiveDateWithPeriod: string },
  ) {
    const [effectiveDate, period] = effectiveDateWithPeriod.split(separator);

    if (period) {
      return SurveyQuery.findActiveBy({ initiativeId, effectiveDate, sourceName, type, period: period as DataPeriods });
    }

    return SurveyQuery.findAllActiveSurveyByMonth(initiativeId, new Date(effectiveDateWithPeriod), sourceName, type);
  }

  /**
   * filter rows where we didn't find a survey
   */
  private filterRows(surveysLoaded: SurveysLoadedByInitiative, aggregatedRows: AggregatedJsonRows) {
    const filtered: AggregatedJsonRows = {}
    for (const initiativeCode in aggregatedRows) {

      for (const effectiveDate in aggregatedRows[initiativeCode]) {
        if (!surveysLoaded[initiativeCode]?.[effectiveDate]) {
          continue
        }
        if (!filtered[initiativeCode]) {
          filtered[initiativeCode] = {}
        }
        filtered[initiativeCode][effectiveDate] = aggregatedRows[initiativeCode][effectiveDate]
      }
    }
    return filtered
  }

  private getQuestionsSheet(workbook: WorkBook) {
    const tabName = this.getSheet(workbook.SheetNames)
    if (!tabName) {
      throw new UserError(
        `Cannot find import sheet. Spreadsheet should contain one of these sheet names: ${this.tabNames.join(', ')}`,
        { sheetNames: workbook.SheetNames }
      );
    }
    return workbook.Sheets[tabName]
  }

  public async parseFile(
    initiativeId: string | ObjectId,
    filepath: string,
    sourceName: string = DefaultBlueprintCode
  ): Promise<ParseFileResult> {


    this.logger.info('Parsing excel file', { initiativeId, filepath });

    const workbook = await this.excelParser.readFile(filepath)
    const sheet = this.getQuestionsSheet(workbook)
    this.logger.info(`Parsed excel file, found ${workbook.SheetNames.length} sheets`, {
      initiativeId,
      sheetNames: workbook.SheetNames
    });

    const jsonData: JsonRow[] = this.excelParser.sheetToJson(sheet)

    const headers = this.excelParser.getHeaders(sheet);

    this.logger.info(`Converted excel file to json, found ${jsonData.length} data rows`, {
      initiativeId,
      rowCount: jsonData.length
    });

    // set unique list of initiativeCode => survey date to be loaded
    const { aggregatedRows, surveysToLoad, rowsErrors } = this.aggregateRows(jsonData, headers)

    const initiativeCodes = Object.keys(aggregatedRows);
    this.logger.info(`Aggregated json rows, found ${initiativeCodes.length} initiative codes, row errors: ${rowsErrors.length}`, {
      initiativeId,
      initiativeCodes: initiativeCodes,
      rowErrors: rowsErrors.slice(0, 100),
    });

    // load active surveys
    const initiativeTree = await InitiativeRepository.getMainTreeChildren(initiativeId)
    const { surveysLoaded, loadErrors } = await this.loadSurveys(
      surveysToLoad,
      initiativeTree,
      sourceName,
      SurveyType.Default,
    )

    const validRows = this.filterRows(surveysLoaded, aggregatedRows)

    const validRowCount = Object.values(validRows).reduce((acc, initiativeRows) => {
      return acc + Object.values(initiativeRows).length;
    }, 0);

    this.logger.info(`Results, found ${validRowCount} valid rows`, {
      initiativeId,
      missingInitiatives: loadErrors.initiatives.missing,
      missingSurveys: Object.values(loadErrors.surveys.missing).flat().length,
    });

    return <ParseFileResult>{
      surveysLoaded,
      errors: {
        ...loadErrors,
        invalidRows: rowsErrors
      },
      validRows
    }
  }

  /**
   * run validation and import
   */
  public async import(updateOptions: SurveyUpdates) {
    const surveyImportData = await this.createSurveyUpdates(updateOptions)

    return this.executeImport(surveyImportData, updateOptions.user);
  }

  // Trigger the parsed data to be imported
  public async executeImport(surveyImportData: SurveyImportData[], user: UserPlain) {
    const results: ResultData[] = []
    for (const importData of surveyImportData) {

      const { initiativeCode, surveyId, effectiveDate, updates } = importData;
      const importedUtrvs = await this.dataImporter.updateActionRequests(updates, user);

      results.push({
        initiativeCode: initiativeCode,
        surveyDate: effectiveDate,
        surveyId: surveyId.toString(),
        utrvs: importedUtrvs.map(u => {
          return {
            id: u.id,
            value: u.value,
            valueData: u.valueData,
            valueType: u.valueType,
            unit: u.unit,
            numberScale: u.numberScale
          }
        })
      })
    }
    return results;
  }

  public async exportBulkImportTemplate(initiativeId: string | ObjectId): Promise<WorkBook> {
    const excel = this.excelParser;
    const workbook = await excel.createBook();

    // first sheet
    const sheetName = 'Import';
    const headers: string[] = ['Level Code', 'Month', 'Year', 'Period', ...Object.values(COLUMNS)];
    const sheet = excel.jsonToSheet([], { header: headers });
    await excel.addToBook(workbook, sheet, sheetName);

    // second sheet
    const initiatives = await InitiativeRepository.getMainTreeChildren(initiativeId);
    const secondSheetName = 'Subsidiary codes';
    const secondSheetHeaders = ['Subsidiary Name', 'Subsidiary Code', 'Creation Date'];

    const data: { [k: string]: number | string | Date }[] = [];
    initiatives.forEach((initiative: InitiativePlain<ObjectId>) => {
      data.push({ name: initiative.name, code: initiative.code, created: initiative.created });
    });

    const secondSheet = excel.jsonToSheet(data);
    await excel.addToBook(workbook, secondSheet, secondSheetName);
    excel.changeHeaders(secondSheet, secondSheetHeaders);

    return workbook;
  }

// Group updates
  public async createSurveyUpdates({ surveysLoaded, validRows, user }: SurveyUpdates) {
    const mapping = DataTransform.getDefaultMapping()
    const results: SurveyImportData[] = []
    for (const initiativeCode in validRows) {
      for (const effectiveDate in validRows[initiativeCode]) {
        const data = validRows[initiativeCode][effectiveDate]
        const survey = surveysLoaded[initiativeCode][effectiveDate]
        if (!survey) {
          continue
        }
        const updates = await this.dataImporter.process({
          data,
          survey,
          user,
          mapping,
        })
        this.logger.info(`Processed survey file import, pending updates ${updates.length}`, {
          surveyId: survey._id.toString(),
          userId: user._id.toString(),
        })
        results.push({
          surveyId: survey._id,
          initiativeCode,
          effectiveDate,
          initiativeId: survey.initiativeId,
          updates,
        })
      }
    }

    return results;
  }
}

let instance: BulkFileImporter;
export const getBulkFileImporter = () => {
  if (!instance) {
    instance = new BulkFileImporter(
      getDataImporter(),
      wwgLogger,
      getExcel(),
    );
  }
  return instance;
}

