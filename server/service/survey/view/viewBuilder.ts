/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import {
  CompositeUtrConfigInterface,
  ImportConfiguration
} from '../../../survey/compositeUtrConfigs';
import { ValueChainCategory } from '../../../util/valueChain';
import { UtrConfig } from '../../../rules/UtrConfig';

export interface ConfigMapping {
  code: string;
  fragments?: ConfigMapping[];
  valueChainComposite?: ConfigMapping[];
  valueChain?: ValueChainCategory;
  importConfigurationData?: ImportConfiguration;
}

export const buildConfigTree = (config: CompositeUtrConfigInterface): ConfigMapping => {
  const {
    compositeUtrCode,
    fragmentUtrConfiguration,
    importConfigurationData,
  } = config;

  const fragments: ConfigMapping[] = [];
  const valueChainComposite: ConfigMapping[] = [];
  const vcCodes = new Map<ValueChainCategory, ConfigMapping[]>();

  const fragmentUtrCodes = UtrConfig.getFragmentUtrCodes(config);
  (fragmentUtrCodes || []).forEach(fragmentCode => {
    const subFragments: ConfigMapping = { code: fragmentCode };
    const sub: ConfigMapping[] = [];
    if (fragmentUtrConfiguration) {
      const valueChainCodes = fragmentUtrConfiguration[fragmentCode];
      if (valueChainCodes) {
        valueChainCodes.forEach(({ code, useComposite }) => {
          if (useComposite) {
            return;
          }
          const vcCode = <ValueChainCategory>code;
          const subFragment = { code: `${fragmentCode}`, valueChain: vcCode };

          if (!vcCodes.has(vcCode)) {
            vcCodes.set(vcCode, []);
          }
          const vcCodeArray = vcCodes.get(vcCode);
          if (vcCodeArray) {
            vcCodeArray.push(subFragment);
          }
          sub.push(subFragment);
        });
      }
    }

    if (sub.length) {
      subFragments.fragments = sub;
    }

    fragments.push(subFragments);
  });

  // All used value chain codes
  vcCodes.forEach((v, k) => {
    valueChainComposite.push({
      code: `${compositeUtrCode}`,
      valueChain: k,
      fragments: v,
    });
  });


  return { code: compositeUtrCode, fragments, valueChainComposite, importConfigurationData };
};
