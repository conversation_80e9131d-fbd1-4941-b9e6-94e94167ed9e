/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Blueprint, SurveyForm } from '../../../repository/BlueprintRepository';
import { SurveyModelPlain } from '../../../models/survey';
import {
  SurveyViewData,
  SurveyViewerRepository,
} from '../../../repository/SurveyViewerRepository';
import { wwgLogger } from '../../wwgLogger';
import {
  CompositeUtrConfigInterface,
  ConfigurationVariableSetup,
  ImportConfiguration,
} from '../../../survey/compositeUtrConfigs';
import { buildConfigTree, ConfigMapping } from './viewBuilder';
import { getMergedConfigs } from '../../../survey/surveyForms';
import { UTrGroupConfigInterface } from '../../../survey/utrGroupConfigs';
import { InitiativePlain } from '../../../models/initiative';
import { ValueChainCategory } from '../../../util/valueChain';
import { CalculationType, ValueRule, VariableMap } from '../../../rules/rule';
import {
  fnPattern,
  replaceExecuteScopeFn,
  replaceOnly,
} from '../../../rules/calculation/formula';
import { ActionList } from '../../utr/constants';
import { reduceTable } from '../../../rules/calculation';
import {
  UtrValueType,
  ValueValidation,
} from "../../../models/public/universalTrackerType";
import { ValueData } from "../../../models/public/universalTrackerValueType";
import { NotApplicableTypes } from '../../../models/universalTrackerValue';

interface MinUTr {
  code: string;
  valueLabel: string;
}

export interface BuildOptions {
  exclude: string[];
  extended: boolean;
  groups: boolean;
}

interface ReturnValueData {
  _id: any;
  label: string;
  code: string;
  lastUpdated: Date;
  initiativeCode: 1;
  utr: {
    code: string; valueLabel: string;
    valueType: UtrValueType;
    valueValidation: ValueValidation;
  };
  status: string;
  value: any;
  valueData: ValueData,
}

interface ResolvedCalculationItem {
  [key: string]: string
}

interface Resolved {
  calculated: number | string;
  calculation: {
    [key: string]: ResolvedCalculationItem[]
  };
}

export class SurveyViewer {
  private universalTrackerCache = new Map<string, MinUTr>();
  private utrvMap: Map<string, SurveyViewData> = new Map();
  private deletedMap: Map<string, SurveyViewData> = new Map();

  constructor(
    private blueprint: Blueprint,
    private initiative: InitiativePlain,
  ) {
  }

  public async createDataView(survey: SurveyModelPlain, options: BuildOptions) {
    const data = await SurveyViewerRepository.loadSurveyData(survey);
    this.processData(data);

    return this.buildView(data, options);
  }

  private processData(data: SurveyViewData[]) {

    data.forEach((utrv) => {
      const { universalTracker: utr, universalTrackerId, deletedDate, _id } = utrv;
      if (!utr) {
        return wwgLogger.error(
          'Failed to lookup universalTracker for id %s utrv: _id %s',
          universalTrackerId,
          _id
        );
      }
      this.universalTrackerCache.set(utr.code, utr);
      if (deletedDate) {
        this.deletedMap.set(
          utrv.uniqueCode + '/deleted/' + deletedDate.toISOString(),
          utrv
        );
        return;
      }
      this.utrvMap.set(utrv.uniqueCode, utrv);
    });
  }

  private buildView(data: SurveyViewData[], options: BuildOptions) {

    const composite: any[] = [];
    const groups: any = [];
    const forms = getMergedConfigs(this.blueprint);

    for (const surveyForm of forms) {
      if (surveyForm.compositeConfig) {
        const c = <CompositeUtrConfigInterface>surveyForm.config;
        const items = buildConfigTree(c);
        composite.push(this.expandItem(items));
      }
      if (surveyForm.utrGroupConfig) {
        groups.push(this.processUtrConfig(surveyForm));
      }
    }

    return {
      code: this.blueprint.code,
      references: <any>[],
      referencedBy: <any>[],
      groups: options.groups ? groups : [],
      data: options.extended ? data : [],
      resolvedConfigs: composite.map(c => {
        options.exclude.forEach(e => {
          c[e] = undefined;
        });
        return c;
      }),
    };
  }

  private processUtrConfig(surveyForm: SurveyForm) {
    const { groupName, utrCodes } = <UTrGroupConfigInterface>surveyForm.config;

    return {
      groupName,
      utrCodes: utrCodes.map(code => this.buildReturnValue(code))
    };
  }

  private buildReturnValue(code: string, valueChain?: ValueChainCategory): ReturnValueData {
    const mainUtrv = this.utrvMap.get(this.initiative.code + '#' + code);
    if (!mainUtrv) {
      // Not in scope? or Deleted permanently
      return <any>{
        code,
        valueChain,
        error: 'data not available for this code',
        valueData: undefined,
        value: 'NO_DATA_AVAILABLE',
      };
    }

    return {
      _id: mainUtrv._id,
      label: mainUtrv.universalTracker.valueLabel,
      code,
      lastUpdated: mainUtrv.lastUpdated,
      initiativeCode: mainUtrv.initiative.code,
      utr: mainUtrv.universalTracker,
      value: this.getMainValue(mainUtrv),
      valueData: mainUtrv.valueData,
      status: mainUtrv.status,
    };
  }

  private getMainValue(mainUtrv: { status: string, value: number | undefined, valueData?: { notApplicableType?: string } }) {

    if (mainUtrv.status === ActionList.Created && mainUtrv.value === undefined) {
      return 'CREATED';
    }
    if (mainUtrv.valueData?.notApplicableType === NotApplicableTypes.NA) {
      return 'NA';
    }
    if (mainUtrv.valueData?.notApplicableType === NotApplicableTypes.NR || mainUtrv.value === undefined) {
      return 'NR';
    }

    return mainUtrv.value;
  }

  private expandItem({ code, fragments, valueChain, valueChainComposite, importConfigurationData }: ConfigMapping): ConfigMapping {

    const expandedData = this.buildReturnValue(code, valueChain);
    const resolvedFragments = fragments ? fragments.map((f) => this.expandItem(f)) : undefined;
    const compositeVc = valueChainComposite ? valueChainComposite.map((f) => {
      return this.expandItem({ ...f, importConfigurationData });
    }) : undefined;

    let resolved: any;
    if (importConfigurationData) {
      resolved = this.resolvedConfiguration(
        expandedData,
        importConfigurationData,
        resolvedFragments
      );
    }

    return {
      ...expandedData,
      fragments: resolvedFragments,
      valueChainComposite: compositeVc,
      importConfigurationData: resolved,
    };
  }

  private resolvedConfiguration(
    expandedData: ReturnValueData,
    importConfigurationData: ImportConfiguration,
    resolvedFragments: ConfigMapping[] = []
  ) {
    const resolved: Resolved = { calculated: this.getMainValue(expandedData), calculation: {} };
    const resolvedVariables = this.buildVariableMap(importConfigurationData, resolvedFragments, resolved);

    const calcValues = importConfigurationData.calculation.values;

    calcValues.forEach((valueRule, i) => {
      resolved.calculation[i + '-' + valueRule.type] = this.getResolvedCalculationItems(valueRule, resolvedVariables);
    });
    return resolved;
  }

  public getResolvedCalculationItems(valueRule: ValueRule, resolvedVariables: VariableMap): ResolvedCalculationItem[] {
    const { type, variables, formula = '' } = valueRule;

    if (type === CalculationType.Text) {
      return [{ [CalculationType.Formula]: formula }];
    }

    if (!variables) {
      return [];
    }

    return variables.map(({ type: variableType, formula = '', weight }) => {
      let formulaWithVariables = replaceOnly(formula, resolvedVariables);
      if (fnPattern.test(formulaWithVariables)) {
        formulaWithVariables = replaceExecuteScopeFn(formulaWithVariables);
      }

      const weightExist = `${weight ? `weight&nbsp: &nbsp${weight}&nbsp` : ''}`

      return {
        [variableType]: `${weightExist} ${formula}=(${formulaWithVariables})`,
      };
    });
  }

  private buildVariableMap(
    importConfigurationData: ImportConfiguration,
    fragments: ConfigMapping[],
    resolved: any
  ) {
    const resolvedVariables: VariableMap = {};

    Object.entries(importConfigurationData.variables).forEach(([v, variables]) => {
      const { valueListCode, code } = variables;
      const option = valueListCode ? `#${valueListCode}` : '';

      const fragmentItem: any = fragments.find(f => f.code === code);
      const value = this.getValue(fragmentItem, variables);

      const valueOne = (value === undefined || value === '') ? '{}' : value;
      resolvedVariables[v] = valueOne;
      resolved[`${v}`] = { code, option, value: valueOne };
    });

    return resolvedVariables;
  }

  public getValue(fragmentItem: any, { valueListCode, getValue }: ConfigurationVariableSetup) {
    if (!fragmentItem) {
      return '';
    }

    const utr = fragmentItem.utr;

    if (!utr) {
      return fragmentItem.valueData?.data?.[valueListCode as string] ?? fragmentItem.value;
    }

    if (typeof getValue === 'function') {
      return getValue({ utrv: fragmentItem, utr });
    }

    if (utr.valueType === UtrValueType.Table && utr.valueValidation?.table) {
      const table = utr.valueValidation.table;
      const tableData = reduceTable(table, fragmentItem.valueData?.table);
      return tableData[valueListCode as string];
    }

    if ([UtrValueType.ValueList, UtrValueType.Text].includes(utr.valueType as UtrValueType)) {
      return fragmentItem.valueData?.data;
    }

    if (valueListCode && UtrValueType.ValueListMulti === utr.valueType) {
      if (Array.isArray(fragmentItem.valueData?.data)) {
        return fragmentItem.valueData?.data?.find((c: string) => c === valueListCode);
      }
    }

    return fragmentItem.valueData?.data?.[valueListCode as string] ?? fragmentItem.value;
  }
}
