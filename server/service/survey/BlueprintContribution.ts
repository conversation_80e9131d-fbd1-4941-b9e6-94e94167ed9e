/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import {
  BlueprintRepositoryInterface,
  getBlueprintRepository,
} from '../../repository/BlueprintRepository'
import { Blueprints } from '../../survey/blueprints'
import UserError from '../../error/UserError'
import { CompositeUtrConfigInterface } from '../../survey/compositeUtrConfigs'
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import UniversalTracker, {
  UniversalTrackerBlueprintMin,
} from '../../models/universalTracker';
import { utrForBlueprintProject } from '../../repository/projections';
import { createEmptyTags } from '../../models/common/universalTrackerTags';
import { UtrConfig } from '../../rules/UtrConfig';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { ObjectId } from 'bson';

export interface BlueprintContributions {
  [k: string]: string[];
}
type ContributionMap = {
  [key in Blueprints]: BlueprintContributions;
};

export class BlueprintContribution {

  private sdgRegex = new RegExp(/sdg\/([1]*[0-9]+\.*[1-2]*[0-9]*)$/);

  private map: ContributionMap = {
    bLab2019Blueprint: {},
    baseline2019: {},
    gri2019: {},
    gri2020: {},
    adapter2020: {},
    adapter2022: {},
    blueprint2022: {},
    materiality2024: {},
  };

  constructor(private blueprintRepo: BlueprintRepositoryInterface) {
  }

  public async getQuestions(blueprintCode: Blueprints): Promise<UniversalTrackerBlueprintMin[]> {
    const blueprint = await this.blueprintRepo.getBlueprint(blueprintCode);
    if (!blueprint) {
      return [];
    }

    return UniversalTracker.find(
      { code: { $in: extractVisibleUtrCodes(blueprint) } },
      utrForBlueprintProject,
    ).lean().exec();
  }

  public async getQuestionsWithContributions(blueprintCode: Blueprints): Promise<UniversalTrackerBlueprintMin[]> {
    const universalTrackers = await this.getQuestions(blueprintCode);
    if (!universalTrackers) {
      return [];
    }
    return this.populateSdgTagsProperty(blueprintCode, universalTrackers);
  }

  public async getQuestionsWithCustomMetrics(
    blueprintCode: Blueprints,
    initiativeId: ObjectId | string
  ): Promise<UniversalTrackerBlueprintMin[]> {
    const [blueprintQuestions, metricGroupQuestions] = await Promise.all([
      this.getQuestionsWithContributions(blueprintCode),
      InitiativeRepository.getInitiativeKpis(initiativeId),
    ]);
    const uniqueUTRs: Map<string, UniversalTrackerBlueprintMin> = new Map();
    blueprintQuestions.forEach((utr) => uniqueUTRs.set(utr.code, utr));
    metricGroupQuestions.forEach((utr) => {
      // Skip if question is already in Map to prevent overwriting with a potentially incomplete version lacking populated SDG tags
      if (!uniqueUTRs.has(utr.code)) {
        uniqueUTRs.set(utr.code, utr);
      }
    });
    return Array.from(uniqueUTRs.values());
  }

  public async populateSdgTagsProperty(blueprintCode: Blueprints, universalTrackers: UniversalTrackerBlueprintMin[]) {
    const contributions = await this.getContributions(blueprintCode);

    universalTrackers.forEach((utr) => {
      if (!utr.tags) {
        utr.tags = createEmptyTags();
      }
      const extraTags = this.convertToSDGCodes(contributions[utr.code]);
      if (Array.isArray(utr.tags.sdg)) {
        utr.tags.sdg.forEach(t => extraTags.push(t));
      }
      utr.tags.sdg = Array.from(new Set(extraTags)).sort();
    });

    return universalTrackers;
  }

  public convertToSDGCodes(tags: string[] | undefined): string[] {
    if (!tags) {
      return [];
    }
    const newTags: string[] = [];
    tags.forEach(tag => {
      const found = tag.match(this.sdgRegex);
      if (found) {
        found.filter(s => !s.startsWith('sdg/')).forEach(f => newTags.push(f));
      }
    })
    return newTags;
  }

  public async getContributions(blueprintCode: Blueprints): Promise<BlueprintContributions> {

    const selectedMap = this.map[blueprintCode];
    if (!selectedMap) {
      throw new UserError(`Blueprint "${blueprintCode}" does not exists`);
    }

    if (Object.keys(selectedMap).length > 0) {
      return selectedMap;
    }

    return this.buildBlueprintMap(blueprintCode);
  }

  private async buildBlueprintMap(blueprintCode: Blueprints): Promise<BlueprintContributions> {

    const blueprint = await this.blueprintRepo.getExpandedBlueprintByCode(blueprintCode);
    if (!blueprint) {
      return {};
    }
    const topLevel = [blueprint];
    for (const r of blueprint.references) {
      const refBlueprint = await this.blueprintRepo.getExpandedBlueprintByCode(r);
      if (refBlueprint) {
        topLevel.push(refBlueprint);
      }
    }

    const selectedMap = this.map[blueprintCode];
    const lookupMap = new Map<string, string[]>();
    for (const bp of topLevel) {
      for (const form of [...bp.forms, ...(bp.additionalConfigs || [])]) {
        if (form.compositeConfig) {
          const config = <CompositeUtrConfigInterface>form.config;
          const fragmentUtrCodes = UtrConfig.getFragmentUtrCodes(config);
          lookupMap.set(config.compositeUtrCode, fragmentUtrCodes);
        }
      }
    }

    // Recursive
    const FindLowest = (codes: string[], sdgCode: string[]) => {
      codes.forEach(code => {
        const fragments = lookupMap.get(code);
        if (fragments) {
          FindLowest(fragments, [...sdgCode, code])
        } else {
          selectedMap[code] = Array.from(new Set([...(selectedMap[code] || []), ...sdgCode]));
        }
      });
    };

    // Start from top score
    const key = 'sdg/score';
    const fragments = lookupMap.get(key);
    if (fragments) {
      FindLowest(fragments, [key])
    }

    return selectedMap;
  }
}

let instance: BlueprintContribution;
export const getBluePrintContribution = () => {
  if (!instance) {
    instance = new BlueprintContribution(getBlueprintRepository());
  }
  return instance;
};
