/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { frameworks, standards } from '@g17eco/core';
import { ActionList } from '../../utr/constants';
import { Scope, SurveyModelPlain, SurveyPlainExtended, SurveyWithInitiative } from '../../../models/survey';
import { SurveyPermissions } from '../SurveyPermissions';
import { AuthenticatedRequest } from '../../../http/AuthRouter';
import UserError from '../../../error/UserError';
import PermissionDeniedError from '../../../error/PermissionDeniedError';
import { toArray, toBoolean } from '../../../http/query';
import BadRequestError from '../../../error/BadRequestError';
import { UserModel } from '../../../models/user';
import { MetricGroupRepository } from '../../../repository/MetricGroupRepository';
import { validVisibility, VisibilityOptions, VisibilityStatus } from './visibilityStatus';
import { UtrvAssuranceStatus } from '../../../models/universalTrackerValue';
import { getBluePrintContribution } from '../BlueprintContribution';
import UniversalTracker from '../../../models/universalTracker';
import { Blueprints, DefaultBlueprintCode } from '../../../survey/blueprints';
import { DataScopeAccess } from "../../../models/dataShare";
import ContextError from "../../../error/ContextError";
import { ObjectId } from 'bson';
import { DownloadScopeType } from '../../../types/download';

/**
 * @deprecated Import from server/types/download.ts to avoid circular dependency
 **/
export interface DownloadDisplayOptions {
  displayUserInput?: boolean;
  displayTag?: boolean;
  displayMetricOverrides?: boolean;
}

/**
 * @deprecated Import from server/types/download.ts to avoid circular dependency
 **/
export interface DownloadScopeData extends Pick<DownloadDisplayOptions, 'displayMetricOverrides'> {
  type?: DownloadScopeType;
  values?: string[];
  statuses?: ActionList[];
  assuranceStatus?: UtrvAssuranceStatus[];
  visibilityStatus?: VisibilityStatus
}

type ScopeQuery = Partial<Pick<Scope<string | ObjectId>, 'standards' | 'frameworks' | 'custom' | 'sdg'>>;
export interface DownloadMultiScope extends DownloadDisplayOptions, Pick<DownloadScopeData, 'statuses' | 'assuranceStatus' | 'visibilityStatus'> {
  access: DataScopeAccess;
  scope: ScopeQuery;
  sourceName?: string;
}

interface ScopeDownloadQueryParams {
  publicQuestionsOnly?: string;
  visibility?: string;
  statuses?: ActionList[];
  assuranceStatus?: UtrvAssuranceStatus[];
  displayMetricOverrides?: boolean;
}

interface DownLoadRequestQueryParams extends ScopeDownloadQueryParams {
  scopeValues?: string[];
}

export type DownLoadRequest = AuthenticatedRequest<any, any, any, DownLoadRequestQueryParams>;
export type DownLoadRequestDoc = AuthenticatedRequest<any, any, { scopeValue: string }, ScopeDownloadQueryParams>;

const validStandards = Object.keys(standards);
const validFrameworks = Object.keys(frameworks);


function isValidVisibilityStatus(visibility: string | undefined): visibility is VisibilityStatus {
  if (!visibility) {
    return false
  }
  return validVisibility.includes(visibility as VisibilityStatus);
}

export interface MultiScopeSetup extends DownloadDisplayOptions {
  scope: ScopeQuery,
  statuses: string[],
  assuranceStatus?: UtrvAssuranceStatus[];
  visibility: VisibilityStatus,
}

const bc = getBluePrintContribution();

export class DownloadScope {

  public static async fromMultiScopeRequest(
    req: AuthenticatedRequest,
    survey: SurveyWithInitiative,
    excludeStrategy: VisibilityOptions['excludeStrategy']
  ): Promise<DownloadMultiScope> {
    return this.createMultiScope(survey, req.user, req.body, excludeStrategy);
  }

  public static getValidScope(scope: ScopeQuery) {
    const validScope = Object.entries(scope ).reduce((acc, [type, scopeValues]) => {
      const t = type as keyof ScopeQuery;
      acc[t] = Array.from(this.validateValues(t, scopeValues));
      return acc
    }, {} as ScopeQuery)
    return validScope;
  }

  public static async createMultiScope(
    survey: SurveyModelPlain,
    user: UserModel,
    scopeData: MultiScopeSetup,
    excludeStrategy: VisibilityOptions['excludeStrategy'],
  ) {
    const { scope, statuses, assuranceStatus, visibility, displayUserInput, displayTag, displayMetricOverrides } = scopeData;
    if (!scope) {
      throw new UserError('Missing download scope')
    }

    const hasAccess = await SurveyPermissions.canAccess(survey, user);
    if (!hasAccess) {
      throw new PermissionDeniedError();
    }

    // Only users with initiative access can get all (unverified) data
    const filteredStatuses = DownloadScope.filterStatuses(statuses, hasAccess);

    return {
      scope: this.getValidScope(scope),
      statuses: filteredStatuses,
      access: DataScopeAccess.Full,
      assuranceStatus,
      visibilityStatus: DownloadScope.getVisibilityStatus({
        visibility,
        hasAccess,
        excludeStrategy,
      }),
      displayUserInput,
      displayTag,
      displayMetricOverrides,
    } as DownloadMultiScope;
  }

  /** @deprecated not used? once removed, should clean up the type DownLoadRequest as well */
  public static async fromRequest(req: DownLoadRequest, survey: SurveyPlainExtended): Promise<DownloadScopeData | undefined> {

    const type = req.params.scopeType;
    if (!this.isValidType(type)) {
      return;
    }

    const { scopeValues = [], publicQuestionsOnly, visibility } = req.query;
    if (!Array.isArray(scopeValues) || scopeValues.length === 0) {
      throw new Error('Please provide "scopeValues" array query parameter')
    }
    const validScopeValues = this.validateValues(type, scopeValues);

    const hasAccess = await SurveyPermissions.canAccess(survey, req.user);
    if (!hasAccess) {
      // @TODO Should always return a valid download scope rather than undefined...
      return;
    }

    // Only users with initiative access can get all (unverified) data
    const filteredStatuses = DownloadScope.filterStatuses(req.query.statuses, hasAccess);
    return {
      type,
      values: Array.from(validScopeValues).sort(),
      statuses: filteredStatuses,
      visibilityStatus: DownloadScope.getVisibilityStatus({
        publicQuestionsOnly: toBoolean(publicQuestionsOnly),
        hasAccess,
        visibility,
      }),
    };
  }

  /**
   * Hard to understand...,
   * following current filtering logic for now.
   *
   *     if (!downloadScope?.statuses) {
   *       aggregateMatch.status = ActionList.Verified;
   *     } else if (downloadScope.statuses.length > 0) {
   *       aggregateMatch.status = { $in: downloadScope.statuses };
   *     }
   *
   * [verified, rejected] = one of the statuses in the array
   * undefined            = only verified
   * []                   = all values
   *
   */
  private static filterStatuses(statuses: string[] | string | undefined, hasAccess: boolean) {

    if (!hasAccess) {
      // No access must mean it's only verified
      return [ActionList.Verified]
    }

    const statusToCheck = toArray(statuses, [])
    if (statusToCheck.length === 0) {
      // Follow current filtering logic, empty means no filtering
      return [];
    }

    const validStatuses = Object.values(ActionList);
    const invalidStatuses = statusToCheck.filter(status => !validStatuses.includes(status as ActionList));
    if (invalidStatuses.length > 0) {
      throw new BadRequestError(`Statuses filter contains not supported values: '${invalidStatuses.join(',')}'`)
    }

    // Return only valid or empty...
    return statusToCheck as ActionList[]
  }

  public static async fromRequestDoc(req: DownLoadRequestDoc, survey: SurveyWithInitiative): Promise<DownloadScopeData> {

    const hasAccess = await SurveyPermissions.canAccess(survey, req.user);
    if (!hasAccess) {
      throw new PermissionDeniedError();
    }
    const { scopeValue } = req.params;
    const { statuses, publicQuestionsOnly, visibility, displayMetricOverrides } = req.query;

    return await this.create({
      survey,
      user: req.user,
      scopeValue,
      statuses,
      publicQuestionsOnly,
      displayMetricOverrides,
      visibility
    });
  }

  public static async create({ survey, user, scopeValue, statuses, publicQuestionsOnly, visibility, displayMetricOverrides }: {
    survey: Pick<SurveyWithInitiative, '_id' | 'initiative' | 'initiativeId' | 'visibleStakeholders' | 'completedDate'>,
    user: UserModel,
    scopeValue?: string,
    statuses?: ActionList[],
    publicQuestionsOnly?: string,
    visibility?: string,
    displayMetricOverrides?: boolean,
  }): Promise<DownloadScopeData> {

    const hasDirectAccess = await SurveyPermissions.canAccess(survey, user);

    // Only users with initiative access can get unverified data
    const type = scopeValue && validFrameworks.includes(scopeValue) ? 'frameworks' : 'standards';

    return {
      type,
      values: scopeValue ? [scopeValue] : undefined,
      statuses: DownloadScope.filterStatuses(statuses, hasDirectAccess),
      visibilityStatus: DownloadScope.getVisibilityStatus({
        publicQuestionsOnly: toBoolean(publicQuestionsOnly),
        hasAccess: hasDirectAccess,
        visibility,
        // Need to get the actual utrvs, so we can display exclusion
        excludeStrategy: VisibilityStatus.ExcludeValuesOnly,
      }),
      displayMetricOverrides,
    };
  }

  public static validateValues(type: keyof Scope, scopeValues: (string | ObjectId)[]) {
    const stringScopeValues = scopeValues.map(v => v.toString());
    switch (type) {
      case 'standards':
        return new Set(stringScopeValues.filter(v => validStandards.includes(v)))
      case 'frameworks':
        return new Set(stringScopeValues.filter(v => validFrameworks.includes(v)))
      default:
        return new Set(stringScopeValues)
    }
  }

  public static isValidType(type: string): type is DownloadScopeType {
    return (<DownloadScopeType[]>['standards', 'frameworks']).includes(type as DownloadScopeType);
  }

  /**
   * Determine download scope visibility status
   */
  private static getVisibilityStatus(options: VisibilityOptions) {

    const {
      hasAccess,
      visibility,
      publicQuestionsOnly,
      excludeStrategy = VisibilityStatus.ExcludeData
    } = options

    if (hasAccess) {
      // Asked for specific data visibility with access
      if (isValidVisibilityStatus(visibility)) {
        return visibility
      }

      // Fallback to publicQuestionsOnly request, otherwise we include everything
      return publicQuestionsOnly ? excludeStrategy : VisibilityStatus.Include
    }

    // Excluding data either due to permissions or as requested
    if ([visibility, excludeStrategy].includes(VisibilityStatus.ExcludeValuesOnly)) {
      return VisibilityStatus.ExcludeValuesOnly;
    }

    return VisibilityStatus.ExcludeData
  }

  /**
   * initial data should be already loaded through initial match (load by ids etc.)
   * and this should serve as more like a filter, for performance reasons
   */
  public static async generateMultiScopeMatch(
    {
      scope,
      access,
      sourceName = DefaultBlueprintCode,
    }: Pick<DownloadMultiScope, 'scope' | 'sourceName' | 'access'>,
    prefix = '',
    idField = 'universalTrackerId'
  ) {

    if (!access) {
      throw new ContextError(`Access level is missing in generateMultiScopeMatch`, {
        scope,
        access,
        sourceName,
        prefix,
        idField,
      })
    }

    if (access === DataScopeAccess.None) {
      return {
        $or: [{ standards: { $in: [] } }] // Will remove everything
      }
    }

    if (Object.keys(scope).length === 0) {
      if (access !== DataScopeAccess.Full) {
        throw new UserError(`Do you have full access to the resource`, {
          debugMessage: 'At least one downloadScope scope',
          access
        })
      }
      return undefined
    }

    const $or = [] as { [k: string]: unknown }[]

    scope.frameworks?.forEach(type => {
      $or.push({
        [`${prefix}tags.${type}`]: { $exists: true, $ne: [] }
      })
    })

    const uniqueType = new Set<string>()
    scope.standards?.forEach(code => {
      $or.push({
        [`${prefix}alternatives.${code}`]: { $exists: true }
      })
      uniqueType.add(code)
    })

    if (uniqueType.size) {
      $or.unshift({
        [`${prefix}type`]: { $in: Array.from(uniqueType) }
      })
    }

    if (scope.custom && scope.custom.length > 0) {
      const universalTrackerIds = await MetricGroupRepository.getUtrIds(scope.custom)
      $or.push({ [idField]: { $in: universalTrackerIds } })
    }

    if (scope.sdg && scope.sdg.length > 0) {
      const sdgUtrCodes = scope.sdg.map((code) => `sdg/${code}`);
      const contributions = await bc.getContributions(sourceName as Blueprints);
      const utrCodesToLoad: string[] = [];
      Object.entries(contributions).forEach(([utrCode, codes]) => {
        if (codes.some((c) => sdgUtrCodes.includes(c))) {
          utrCodesToLoad.push(utrCode);
        }
      });

      const sdgUtrContributingIds = await UniversalTracker.find({ code: { $in: utrCodesToLoad } }, { _id: 1 })
        .lean()
        .exec();

      $or.push({ [idField]: { $in: sdgUtrContributingIds.map((u) => u._id) } });
    }

    if ($or.length === 0) {
      if (access !== DataScopeAccess.Full) {
        throw new UserError(`Do you have full access to the resource`, {
          debugMessage: 'At least one downloadScope scope',
          access,
        })
      }
      return undefined
    }

    return { $or }
  }
  public static async generateSubgroupMatch({ group, subGroups }: { group: string; subGroups: string[] }) {
    const isStandard = standards[group];
    const isFramework = frameworks[group];
    const $or = [] as { [k: string]: unknown }[];
    if (isStandard) {
      $or.push({ typeTags: { $in: subGroups } });
      $or.push({ [`alternatives.${group}.typeTags`]: { $exists: true, $in: subGroups } });
    } else if (isFramework) {
      $or.push({ [`tags.${group}`]: { $exists: true, $in: subGroups } });
    }
    return $or.length > 0 ? { $or } : undefined;
  }
}
