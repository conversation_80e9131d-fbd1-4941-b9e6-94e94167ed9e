/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { getMateriality, MaterialityMap } from '../../../models/initiative';
import { Industry } from '../../../types/initiative';

interface MaterialityData {
  industry?: Industry,
  materialityMap?: MaterialityMap,
}

export const getMaterialityCode = (initiative: MaterialityData, materiality: string[]) => {
  const materialityData = getMateriality(initiative.industry, initiative.materialityMap) || {};

  const map: any = { none: 0, low: 33, medium: 66, high: 100 };
  const materialityFilters = materiality.reduce((a, m) => {
    if (map[m] !== undefined) {
      a.push(map[m])
    }
    return a;
  }, <number[]>[]);

  return Object.entries(materialityData).reduce((a, [k, v]: any) => {
    if (materialityFilters.includes(v)) {
      a.push(k);
    }

    return a;
  }, <string[]>[]);
};
