/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import type { RequestScope } from '../model/DelegationScope';

const addCode = (type: string, code: string) => type === 'sdg' ? `sdg/${code}` : code;
export const getCombinedScopeGroups = function (scopeGroups: RequestScope[]) {
  return scopeGroups.reduce((a, c) => {

    if (!a[c.scopeType]) {
      a[c.scopeType] = new Set();
    }
    a[c.scopeType].add(addCode(c.scopeType, c.code));
    c.scopeTags?.forEach(subCode => a[c.scopeType].add(addCode(c.scopeType, subCode)));

    return a;
  }, <{ [key: string]: Set<string> }>{});
};
