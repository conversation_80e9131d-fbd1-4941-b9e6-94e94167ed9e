/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UTrGroupConfigInterface } from '../../survey/utrGroupConfigs';
import { CompositeUtrConfigInterface } from '../../survey/compositeUtrConfigs';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import { Blueprint, SurveyForm } from '../../repository/BlueprintRepository';
import { wwgLogger } from '../wwgLogger';
import { SurveyActionDataAggregation } from '../../models/survey';
import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { ValueList } from '../../models/public/valueList';
import { UtrConfig } from '../../rules/UtrConfig';


export interface SurveyAction {
  list: ValueList[];
  fragmentUniversalTracker: UniversalTrackerPlain[];
  fragmentUniversalTrackerValues: UniversalTrackerValuePlain[];
}

interface ValueChainUtrvs {
  code: string,
  initiativeId: string
}

interface GroupQuestion {
  utr: UniversalTrackerPlain,
  valueChain: ValueChainUtrvs[];
}

export interface QuestionGroup extends Pick<UTrGroupConfigInterface, 'groupName' | 'groupData' | 'groupId'> {
  questions: GroupQuestion[];
}


function resolveValueList(utr: UniversalTrackerPlain, list: ValueList[]) {
  if (!utr || !utr.valueValidation || !utr.valueValidation.valueList) {
    return;
  }

  if (utr.valueValidation.valueList.listId) {
    const listId = String(utr.valueValidation.valueList.listId);
    const resolvedList = list.find(l => String(l._id) === listId);
    utr.valueValidation.valueList.list = resolvedList ? resolvedList.options : [];
  }
}


export class SurveyCalculator {

  public static async processSurveyActions(surveyAction: SurveyAction & any, blueprint: Blueprint) {
    surveyAction.config = blueprint;
    surveyAction.questionGroups = SurveyCalculator.createUtrGroupQuestions(surveyAction, blueprint.forms);
  }

  /**
   * fragmentUniversalTracker will contain all subFragmentUniversalTracker utr as well
   */
  public static findSurveyUtr(code: string, surveyAction: Pick<SurveyActionDataAggregation, 'fragmentUniversalTracker'>) {
    return surveyAction.fragmentUniversalTracker?.find((utr) => utr.code === code);
  }

  private static convertUtrCodesToQuestions(
    surveyAction: Pick<SurveyActionDataAggregation, 'list' | 'fragmentUniversalTracker'>,
    codes: string[],
  ): GroupQuestion[] {
    return codes.map((utrCode) => {
      const question: Partial<GroupQuestion> = {};

      question.utr = SurveyCalculator.findSurveyUtr(utrCode, surveyAction);
      if (!question.utr) {
        return;
      }
      resolveValueList(question.utr, surveyAction.list);

      return question as GroupQuestion;
    }).filter((question) => !!question) as GroupQuestion[];
  }

  public static createUtrGroupQuestions(
    surveyAction: Pick<SurveyActionDataAggregation, 'list' | 'fragmentUniversalTracker'>,
    configs: SurveyForm[],
  ) {
    const questionGroups: QuestionGroup[] = [];
    for (const c of configs) {
      if (!c.utrGroupConfig) {
        continue;
      }

      const config = <UTrGroupConfigInterface>c.config;
      questionGroups.push({
        groupName: config.groupName,
        groupId: config.groupId,
        groupData: config.groupData,
        questions: SurveyCalculator.convertUtrCodesToQuestions(
          surveyAction,
          config.utrCodes,
        )
      });
    }
    return questionGroups;
  }

  public static createQuestionGroups(
    surveyAction: Pick<SurveyActionDataAggregation, 'list' | 'sourceName' | 'fragmentUniversalTracker'>,
    configs: SurveyForm[],
  ): QuestionGroup[] {

    const questionGroups = [];
    for (const c of configs) {
      const questionGroup: QuestionGroup & { utr?: UniversalTrackerPlain } = {
        groupName: '',
        groupData: undefined,
        questions: []
      };

      let config;
      let utrCodes: string[] = [];
      if (c.utrGroupConfig) {
        config = <UTrGroupConfigInterface>c.config;
        questionGroup.groupName = config.groupName;
        utrCodes = config.utrCodes;
      } else if (c.compositeConfig) {
        config = <CompositeUtrConfigInterface>c.config;
        if (config.ownerSourceName && config.ownerSourceName !== surveyAction.sourceName) {
          continue;
        }

        if (!config) {
          wwgLogger.error(`[SurveyCalculator] - Failed to resolve survey "${c.compositeConfig}" configuration`);
          continue;
        }

        const groupUtr = SurveyCalculator.findSurveyUtr(config.compositeUtrCode, surveyAction);
        if (!groupUtr) {
          wwgLogger.error(`[SurveyCalculator] - Failed to resolve survey config compositeUtrCode "${config.compositeUtrCode}"`);
          continue;
        }
        questionGroup.utr = groupUtr;
        questionGroup.groupName = config.groupName || groupUtr.typeCode || '';
        utrCodes = UtrConfig.getFragmentUtrCodes(config);
      }

      questionGroup.groupData = config?.groupData;
      questionGroup.questions = SurveyCalculator.convertUtrCodesToQuestions(
        surveyAction,
        utrCodes,
      );

      questionGroups.push(questionGroup);
    }
    return questionGroups;
  }
}
