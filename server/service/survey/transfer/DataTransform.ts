/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  InputRowData,
  InputTableData,
  RowData,
  TableData,
  ValueData,
  ValueDataInput,
} from '../../../models/public/universalTrackerValueType';
import { LinkDocument } from '../../../models/document';
import {
  ColumnType,
  TableColumn,
  UniversalTrackerPublic,
  UtrValueType,
} from '../../../models/public/universalTrackerType';
import { UniversalTrackerRepository } from '../../../repository/UniversalTrackerRepository';
import { ObjectId } from 'bson';
import { Option } from '../../../models/public/valueList';
import { extractListId } from '../../utr/utrUtil';
import { ValueListRepository } from '../../../repository/ValueListRepository';
import {
  NotApplicableTypes,
  UniversalTrackerValuePlain,
} from '../../../models/universalTrackerValue';
import { getValueArray } from '../../../util/array';
import { ActionList } from '../../utr/constants';
import { MetricUnitManager, UnitTypes } from '../../units/MetricUnitManager';
import { tryCalculation } from '../../../rules/calculation/formula';
import { ReportTypeColumnOptions } from './ImportTemplateExcel';
import { UniversalTrackerPlain } from '../../../models/universalTracker';
import { getPreferredTypeCode, getPreferredValueLabel } from '../../../service/assurance/csvContext';
import { getCellValueWithFormat, getFormattedValue } from '../../custom-report/utils';
import { FormattedCell } from '../../custom-report/ReportDataResolver';
import { UtrOverridesMap } from '../../initiative/InitiativeUniversalTrackerService';
import { MetricsMapping } from '../constants';
import { isCorrectNumberScaleCode } from '../../../service/units/utils';
import { SurveyModelPlain } from '../../../models/survey';
import { SupportedMeasureUnits } from '../../units/unitTypes';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import UserError from '../../../error/UserError';

type ProjectUtr = { [P in keyof Required<UtrData>]: 1 };
export type UtrData = Pick<
  UniversalTrackerPublic<ObjectId>,
  | '_id'
  | 'code'
  | 'valueValidation'
  | 'valueType'
  | 'valueLabel'
  | 'unit'
  | 'unitType'
  | 'type'
  | 'name'
  | 'alternatives'
> &
  Pick<UniversalTrackerPlain, 'numberScale' | 'typeCode'>;
type MappingType = string | string[];

export interface TransformMapping<T = MappingType> {
  utrCode: T;
  utrName: T,
  columnCode: T;
  value: T;
  note: T;
  columnName: T;
  notApplicableType: T;
  isImportVerified: T;
  externalEvidenceLinks: T;
  internalEvidenceLinks: T;
  unit: T;
  numberScale: T;
}

export interface TransformOptions {
  mapping?: Partial<TransformMapping>;
  utrs?: UtrData[];
  preferredTypes?: string[];
  utrOverridesMap?: UtrOverridesMap;
  metricsMapping?: MetricsMapping;
}

type Value = string | undefined | number;

export interface MinTransformData {
  utrCode: string;
  note?: string;
  utrvId?: ObjectId | string;
  utrId?: ObjectId | string;

  value?: Value;
  values?: Value[];
  columnCode?: string;
  columnName?: string;
  notApplicableType?: string;
  isImportVerified?: string;
  externalEvidenceLinks?: string[];
  internalEvidenceLinks?: string[];
  unit?: string;

  [key: string]: unknown;
}

export interface UtrvUpdate<T = unknown> {
  utrCode: string;
  utrId: ObjectId | string;
  utrvId?: ObjectId | string;
  note?: string;
  value?: number;
  valueData?: ValueData<T>;
  evidenceLinks?: LinkDocument[];
  isImportVerified?: boolean;
  unit?: string;
  numberScale?: string;
}

export interface UtrvUpdateInputData<T extends Record<string, RowValue> = Record<string, RowValue>> extends UtrvUpdate<T> {
  valueData?: ValueDataInput<T>;
}


export type ValidatedData = Record<string, RowValue>;
type RowValue = string | number | undefined;

interface ObjectData {
  valueData: {
    data: Record<string, RowValue>
  };
}

/** Represent export value */
type SingleValue = Value | FormattedCell;

/** Export version of utr broken down per complex type option or column (table) */
export interface QuestionRow {
  utrId: string;
  utrvId: string;
  utrCode: string;
  question: string;
  columnCode?: string;
  typeCode?: string;
  columnName?: string;
  valueType: string;
  value: SingleValue;
  /** Only used by tables to populate column multiple values **/
  values?: Value[];
  notApplicableType: string | undefined;
  unit?: string | undefined;
  numberScale?: string | undefined;
  note: string | undefined;
  options?: Readonly<Option[]>;
  optionsListId?: ObjectId;
  effectiveDate: string | Date | undefined;
  type: string | undefined;
  isImportVerified: boolean;
  unitType?: string;
}

interface ConvertToRowsParams {
  utr: UtrData;
  utrv: UniversalTrackerValuePlain;
  valueListMap: Map<string, Option[]>;
  preferredTypes: string[];
  utrOverridesMap?: UtrOverridesMap;
}

type DestinationConversion = Pick<UtrData, 'unit' | 'unitType' | 'numberScale'>;

type TableInputParams = {
  columnCode: string;
  utrId: string;
  /** Used by valueList types */
  options: Option[] | undefined;
  utrOverridesMap: UtrOverridesMap | undefined;
  /** Full answer data with at least one empty row [[]] **/
  tableData: TableData;
};

export class DataTransform {
  constructor(private logger: LoggerInterface) {}

  public static getDefaultMapping() {
    return {
      // Update this array if terminology changes (use plain text to avoid compatibility issues with old imports)
      utrCode: ['utrCode', 'QuestionCode', 'Question Code (Locked)', 'MetricCode', 'Metric Code (Locked)'],
      utrName: ['utrName', 'Metric (Locked)'],
      typeCode: ['typeCode', 'TypeCode', 'Type Code (Locked)'],
      columnCode: ['columnCode', 'optionCode', 'OptionCode', 'Option Code (Locked)'],
      columnName: ['columnName', 'option', 'Option', 'Option (Locked)'],
      value: ['value', 'Value', 'Value 1'],
      note: ['note', 'Comment', 'Further Explanation / Notes'],
      valueType: ['valueType', 'ValueType', 'Value Type'],
      notApplicableType: ['notApplicableType', 'Not Applicable or Not Reported', 'NotApplicableType', 'Reporting Type', 'Reporting/NA/NR'],
      isImportVerified: ['Verified(yes/no)', 'Verified'],
      externalEvidenceLinks: ['ExternalEvidenceLinks', 'External Evidence Links'],
      internalEvidenceLinks: ['InternalEvidenceLinks', 'Internal Evidence Links'],
      unit: ['unit', 'Unit'],
      numberScale: ['numberScale', 'NumberScale', 'Number Scale', 'Number Scale (Locked)'], // Support locked version for old templates
    };
  }

  public async transformToRows(utrvs: UniversalTrackerValuePlain[], options: TransformOptions): Promise<QuestionRow[]> {

    const utrs = options.utrs ?? await this.findUtrs({ _id: { $in: utrvs.map(v => v.universalTrackerId) } });
    const valueList = await this.getValueList(utrs)
    const valueListMap = new Map(valueList.map(list => [String(list._id), list.options]))

    const utrLookup = new Map(utrs.map(utr => {
      const vl = utr.valueValidation?.valueList;
      // Expand valueList
      if (vl?.listId && !vl.list) {
        vl.list = valueListMap.get(String(vl.listId));
      }
      return [String(utr._id), utr];
    }));

    const rows: QuestionRow[] = [];
    utrvs.forEach(utrv => {
      const utr = utrLookup.get(String(utrv.universalTrackerId));
      if (utr) {
        const questionRows: QuestionRow[] = this.convertToRows({
          utrv,
          utr,
          valueListMap,
          preferredTypes: options.preferredTypes ?? [],
          utrOverridesMap: options.utrOverridesMap,
        });
        rows.push(...questionRows);
      }
    });

    return rows;
  }

  /**
   * used by data importer to process rows and convert to UtrvUpdate
   */
  public async transform(
    data: unknown | MinTransformData[],
    options: TransformOptions & { survey: Pick<SurveyModelPlain, 'unitConfig'> }
  ): Promise<UtrvUpdate[]> {
    const mapping = this.getMapping(options);
    const transformedData: MinTransformData[] = this.transformData(data, mapping);
    if (transformedData.length === 0) {
      return [];
    }

    const surveyCurrency = options.survey.unitConfig[SupportedMeasureUnits.currency];
    const utrs = options.utrs ?? await this.loadUtrs(transformedData)
    const valueList = await this.getValueList(utrs)
    const valueListMap = new Map(valueList.map(list => [String(list._id), list.options]))
    const calculationColumnsMap = new Map<string, TableColumn[]>();
    const utrLookup = new Map(utrs.map(utr => {
      const vl = utr.valueValidation?.valueList;
      // Expand valueList
      if (vl?.listId && !vl.list) {
        vl.list = valueListMap.get(String(vl.listId));
      }

      // Populate table columns
      const columns = utr.valueValidation?.table?.columns;
      if (columns) {
        columns.forEach(col => {
          if (!col.listId) {
            return;
          }

          if (!col.options || col.options.length === 0) {
            col.options = valueListMap.get(String(col.listId));
          }
        })
      }

      const calculationColumns = this.getTableCalculationColumns(utr);
      if (calculationColumns) {
        calculationColumnsMap.set(utr.code, calculationColumns);
      }

      return [utr.code, utr];
    }));

    const dataMap = new Map<string, UtrvUpdate>();

    const flatData = this.flattenTableColumnsToRows(transformedData, utrLookup);
    flatData.forEach(d => {
      const utr = utrLookup.get(d.utrCode);
      if (!utr) {
        return;
      }

      const customUnitText = this.getCustomUnitText(d, utr);
      // For currency add log if importing currency is different with survey config currency
      if (this.getUnitType(d, utr) === UnitTypes.currency && customUnitText !== surveyCurrency) {
        this.logger.error(
          new UserError('Conflicted currency import detected', {
            surveyCurrency,
            importingCurrency: d.unit,
          })
        );
      }

      const update = dataMap.get(utr.code);
      if (update) {
        update.valueData = this.resolveValueData({
          data: d,
          utr,
          importData: transformedData,
          update,
          currency: surveyCurrency,
        });
        const { convertedValue, defaultUnit, defaultNumberScale } = this.getDefaultData({
          data: d,
          utr,
          update,
          currency: surveyCurrency,
        });
        update.value = convertedValue;
        update.unit = defaultUnit;
        update.numberScale = defaultNumberScale;
        update.note = this.updateNote(d, customUnitText, update);
        return;
      }

      const utrvUpdate: UtrvUpdate = {
        utrCode: utr.code,
        utrId: utr._id,
        valueData: this.resolveValueData({ data: d, utr, importData: flatData, update, currency: surveyCurrency }),
        note: this.updateNote(d, customUnitText),
        isImportVerified: (d.isImportVerified ?? '').toLocaleLowerCase() === 'yes',
        evidenceLinks: this.getEvidenceLinks(d),
      };

      const { convertedValue, defaultUnit, defaultNumberScale } = this.getDefaultData({
        data: d,
        utr,
        update: utrvUpdate,
        currency: surveyCurrency,
      });
      utrvUpdate.value = convertedValue;
      utrvUpdate.unit = defaultUnit;
      utrvUpdate.numberScale = defaultNumberScale;

      if (this.shouldSkipUpdate(utr, utrvUpdate)) {
        return;
      }

      const isNotApplicableType = Boolean(utrvUpdate.valueData?.notApplicableType);
      if (isNotApplicableType) {
        delete(utrvUpdate.value);
      }

      dataMap.set(utr.code, utrvUpdate);
    })

    // Handle calculation columns
    dataMap.forEach((value, key) => {
      const calculationColumns = calculationColumnsMap.get(key) || [];
      if (calculationColumns.length > 0) {
        const utrvUpdate = this.updateCalculationValues(calculationColumns, value);
        dataMap.set(key, utrvUpdate);
      }
    })

    return Array.from(dataMap.values());
  }

  // Transform multiple value columns back to each value per row for Table type question.
  private flattenTableColumnsToRows(data: MinTransformData[], utrLookup: Map<string, UtrData>) {
    const flatData = [];
    let utrCode = '';
    let rows = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const utr = utrLookup.get(row.utrCode);
      if (!utr) {
        continue;
      }

      if (utr.valueType !== UtrValueType.Table) {
        flatData.push(row);
        continue;
      }

      utrCode = row.utrCode;
      const values = row.values || [];
      rows.push(values.map((value) => ({ ...row, value })));

      if (i === data.length - 1 || data[i+1].utrCode !== utrCode) {
        const rowsToAdd = [];
        if (rows.length && rows[0].length) {
          const columnsLength = rows.length;
          const rowsLength = rows[0].length;
          for (let i = 0; i < rowsLength; i++) {
            for (let j = 0; j < columnsLength; j++) {
              rowsToAdd.push(rows[j][i]);
            }
          }
          flatData.push(...rowsToAdd);
        }
        rows = [];
      }
    }

    return flatData;
  }

  private updateCalculationValues(calculationColumns: TableColumn[], value: UtrvUpdate) {
    const rows = value.valueData?.table || [];
    const newCalculatedRows = rows.map((row) => this.recalculateRowColumns(row, calculationColumns));

    return {
      ...value,
      valueData: {
        table: newCalculatedRows,
        input: { table: newCalculatedRows as InputTableData, unit: undefined, numberScale: undefined },
      },
    };
  }

  private recalculateRowColumns(row: RowData[], calculationColumns: TableColumn[]) {
    const initialValue: { [key: string]: any } = {};
    // delete previous calculation values in the row
    const rowWithoutCalculation = row.filter(col => !calculationColumns.some(item => item.code === col.code));

    const columnValues = rowWithoutCalculation.reduce((a, c) => {
      a[c.code] = c.numberScale ? MetricUnitManager.convertUnit(c.value, c.numberScale, 'single') : c.value;
      return a;
    }, initialValue);

    const recalculated = calculationColumns
      .map(c => {
        return {
          code: c.code,
          value: tryCalculation({
            formula: c.calculation?.formula || "",
            variables: columnValues,
          }),
        }
      })

    return [...rowWithoutCalculation, ...recalculated];
  }

  private getUnitType(d: MinTransformData, utr: Pick<UtrData, 'unitType' | 'valueType' | 'valueValidation'>) {
    if (utr.valueType === UtrValueType.Table) {
      return this.getTableColumnCode(d, utr)?.unitType;
    }
    return utr.unitType;
  }

  private getCustomUnitText(d: MinTransformData, utr: UtrData) {
    const rawUnitText = d.unit;
    const unitType = this.getUnitType(d, utr);
    if (!rawUnitText || MetricUnitManager.isValidUnitCodeForUnitType(rawUnitText, unitType)) {
      return undefined;
    }
    const unitCode = MetricUnitManager.getUnitCodeByName(rawUnitText, unitType);
    if (unitCode) {
      return undefined;
    }
    // unitText is not valid unit, prefix "Unit" and return
    return `Unit: ${rawUnitText}`;
  }

  private updateNote(d: MinTransformData, customNote: string = '', update?: UtrvUpdate) {
    const parts: string[] = [];

    if (update?.note) {
      parts.push(update?.note);
    }

    if (typeof d.note === 'string' && d.note) {
      parts.push(d.note);
    }

    if (customNote) {
      parts.push(customNote);
    }

    return parts.length ? parts.join('\n') : undefined;
  }

  private getDefaultData({
    data,
    utr,
    update,
    currency,
  }: {
    data: MinTransformData;
    utr: UtrData;
    update?: UtrvUpdate;
    currency: string;
  }) {
    switch (utr.valueType) {
      case UtrValueType.Percentage: {
        return this.convertUnitNumberScaleData({ data, utr });
      }
      case UtrValueType.Number: {
        return this.convertUnitNumberScaleData({ data, utr, currency });
      }
      case UtrValueType.NumericValueList: {
        return {
          convertedValue: this.calculateUnitNumericValueList(update),
          defaultUnit: utr.unit,
          defaultNumberScale: utr.numberScale,
        };
      }
      default:
        return {
          convertedValue: undefined,
          defaultUnit: undefined,
          defaultNumberScale: undefined,
        };
    }
  }

  private calculateUnitNumericValueList(update?: UtrvUpdate) {
    if (typeof update?.valueData?.data === 'object' && update?.valueData?.data !== null) {
      const values = Object.values(update.valueData.data);
      if (values.length > 0) {
        return values.reduce((a, value) => !isNaN(value) ? (a ?? 0) + Number(value) : a, 0)
      }
    }
    return undefined;
  }

  private calculateInputValueNumericValueList(update?: UtrvUpdate) {
    if (typeof update?.valueData?.input?.data === 'object' && update?.valueData?.input.data !== null) {
      const values = Object.values(update.valueData.input.data);
      if (values.length > 0) {
        return values.reduce((a, value) => !isNaN(value) ? (a ?? 0) + Number(value) : a, 0)
      }
    }
    return undefined;
  }

  private loadUtrs(data: MinTransformData[]): Promise<UtrData[]> {

    const codes: string[] = [];
    const ids: ObjectId[] = [];
    data.forEach(d => {
      if (d.utrCode) {
        codes.push(d.utrCode)
      }
      if (d.utrId) {
        ids.push(new ObjectId(d.utrId));
      }
    });

    const condition: Record<string, any> = {
      $or: [{ _id: { $in: ids } }, { code: { $in: codes } }],
    }
    return this.findUtrs(condition);
  }

  private findUtrs(condition: Record<string, any>) {
    const project: ProjectUtr = {
      _id: 1,
      code: 1,
      valueValidation: 1,
      valueLabel: 1,
      valueType: 1,
      typeCode: 1,
      unit: 1,
      unitType: 1,
      numberScale: 1,
      type: 1,
      name: 1,
      alternatives: 1,
    };
    return UniversalTrackerRepository.find(condition, project, { lean: true });
  }

  public getMapping(options: TransformOptions): TransformMapping {
    return {
      ...DataTransform.getDefaultMapping(),
      ...options.mapping,
    }
  }

  private transformData(initialData: unknown | ValidatedData[], mapping: TransformMapping): MinTransformData[] {
    const data: MinTransformData[] = [];
    if (!Array.isArray(initialData)) {
      return data;
    }
    const maxValues = this.generateMaxValuesMap(initialData, mapping);

    initialData.forEach((d: ValidatedData) => {
      const utrCode = this.getStringMappedValue(d, mapping.utrCode);
      if (utrCode) {

        const maxLength = maxValues.get(utrCode);

        data.push({
          utrCode,
          columnCode: this.getStringMappedValue(d, mapping.columnCode),
          value: this.getMappedValue(d, mapping.value),
          values: this.getMappedValues(d, mapping.value, maxLength),
          note: this.getStringMappedValue(d, mapping.note),
          notApplicableType: this.getStringMappedValue(d, mapping.notApplicableType),
          utrvId: typeof d.utrvId === 'number' ? String(d.utrvId) : d.utrvId,
          utrId: typeof d.utrId === 'number' ? String(d.utrId) : d.utrId,
          isImportVerified: this.getStringMappedValue(d, mapping.isImportVerified),
          externalEvidenceLinks: getValueArray(this.getStringMappedValue(d, mapping.externalEvidenceLinks)),
          internalEvidenceLinks: getValueArray(this.getStringMappedValue(d, mapping.internalEvidenceLinks)),
          unit: this.getStringMappedValue(d, mapping.unit),
          numberScale: this.getStringMappedValue(d, mapping.numberScale),
        })
      }
    })

    return data;
  }

  public getMappedValue(column: ValidatedData, mappingCode: MappingType): RowValue {
    const codeOptions = Array.isArray(mappingCode) ? mappingCode : [mappingCode];
    for (const prop of codeOptions) {
      if (column[prop] !== undefined) {
        return column[prop];
      }
    }
  }

  public getStringMappedValue(column: ValidatedData, mappingCode: MappingType) {
    const mappedValue = this.getMappedValue(column, mappingCode);
    return typeof mappedValue === 'number' ? String(mappedValue) : mappedValue;
  }

  private getMappedValues(row: ValidatedData, mappingCode: MappingType, maxLength = 1)  {
    // Fill in the values to ensure the flattening later on will pick
    // the right properties, as some values are missing if not set
    // { Value 1: 1, Value 2: 55, Value 5: 200, Value 6: 10 } etc.
    const values: (string | number | undefined)[] = Array(maxLength).fill(undefined);

    for(const key of Object.keys(row)) {
      // Assumption here is "Value 1", "Value 2" etc.
      const [prefixKey, valueNumber] = key.split(' ');

      const isMatchedMappingCode = mappingCode === prefixKey || mappingCode.includes(prefixKey)
      const hasNumberPostfix = valueNumber === undefined || !isNaN(valueNumber as any);

      // Value 1 => values[hasNumberPostfix -1] = row[key]
      if (isMatchedMappingCode && hasNumberPostfix) {
        // "Value 1" => 1, Original "Value" should be treated as Value 1 as well
        const headerNumber = valueNumber ? Number(valueNumber) : 1;
        const index = Number(headerNumber) - 1;
        values[index] = row[key]
      }
    }

    return values;
  }

  /**
   * Figure out the max rows we will need to fill in for multi-row table
   * to preserve the expected row column matrix
   *
   *         Value 1, Value 2, Value 3,
   * col1    a                 c
   * col2                      c
   * col3    a
   *
   * Should create 3 rows, as based on original code only when existing column
   * is found it will create new row. Therefore, we need to populate max length
   * for each row to fill in empty column with no value, that will preserve the
   * rows structure.
   */
  private generateMaxValuesMap(initialData: ValidatedData[], mapping: TransformMapping) {
    const maxValues = new Map<string, number>;
    initialData.forEach(row => {
      const utrCode = this.getStringMappedValue(row, mapping.utrCode);
      if (utrCode) {

        const mappingCode = mapping.value;

        let v = maxValues.get(utrCode);
        if (!v) {
          v = 1;
          maxValues.set(utrCode, v);
        }

        const highestKeyLength = Object.keys(row).reduce((acc, key) => {
          const [keyPrefix, valueNumber] = key.split(' ');
          const isMatchedMappingCode = mappingCode === keyPrefix || mappingCode.includes(keyPrefix)

          if (isMatchedMappingCode) {
            if (valueNumber === undefined || !isNaN(valueNumber as any)) {
              const maxNumber = Number(valueNumber);
              return maxNumber > acc ? maxNumber : acc
            }
          }
          return acc;
        }, 1);

        if (highestKeyLength > v) {
          maxValues.set(utrCode, highestKeyLength)
        }
      }
    })
    return maxValues;
  }

  private resolveValueData({
    data,
    utr,
    importData,
    update,
    currency,
  }: {
    data: MinTransformData;
    utr: UtrData;
    importData: MinTransformData[];
    update?: UtrvUpdate;
    currency: string;
  }): ValueData | undefined {
    if (this.isNotApplicableType(data, update)) {
      return this.resolveValueDataForNotApplicableType(data);
    }

    switch (utr.valueType) {
      case UtrValueType.Text:
      case UtrValueType.Date:
        return { data: data.value, input: { data: data.value, unit: undefined, numberScale: undefined } };
      case UtrValueType.ValueList:
        return this.resolveValueList(data, utr);
      case UtrValueType.ValueListMulti:
        return this.resolveValueListMulti(data, utr);
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList:
        return this.resolveComplexValueList({ row: data, utr, importData, update, currency });
      case UtrValueType.Table:
        return this.resolveTable({ row: data, utr, update, currency });
      case UtrValueType.Number:
      case UtrValueType.Sample:
      case UtrValueType.Percentage:
      default:
        return {
          input: {
            value: data.value as number, // same as getValue fn
            unit: MetricUnitManager.getUnitCode(data.unit, utr.unitType, currency),
            numberScale: this.getValidNumberScaleData(data),
          },
        };
    }
  }

  private isNotApplicableType(d: MinTransformData, update: UtrvUpdate | undefined) {
    // Already set notApplicableType
    if (update?.valueData?.notApplicableType) {
      return true
    }

    return [
      NotApplicableTypes.NA,
      NotApplicableTypes.NR,
      ReportTypeColumnOptions.NA,
      ReportTypeColumnOptions.NR,
    ].includes(d.notApplicableType as ReportTypeColumnOptions | NotApplicableTypes);
  }

  private resolveValueDataForNotApplicableType(d: MinTransformData) {
    return {
      notApplicableType: [NotApplicableTypes.NR, ReportTypeColumnOptions.NR].includes(
        d.notApplicableType as NotApplicableTypes | ReportTypeColumnOptions
      )
        ? NotApplicableTypes.NR
        : NotApplicableTypes.NA,
    };
  }

  private getValueListOptions(utr: UtrData): Option[] | undefined {
    const valueList = utr.valueValidation?.valueList;
    if (!valueList) {
      return undefined;
    }

    return valueList.list ?? valueList.custom
  }

  /**
   * Code takes priority
   * Does a case-insensitive search by option label as fallback
   * If allowCustomOptions is true then return a new option
   */
  private resolveOption(options: Option[], value: string | unknown, allowCustomOptions: boolean | undefined) {
    const byCode = options.find(l => l.code === value);
    if (byCode) {
      return byCode
    }

    if (typeof value !== 'string') {
      return
    }

    // Must be string now
    const cleanValue = value.trim().toLowerCase()
    const existedOption = options.find(l => l.name.trim().toLowerCase() === cleanValue);
    if (existedOption) {
      return existedOption;
    }

    if (allowCustomOptions) {
      return {
        code: value.trim(),
        name: value.trim()
      }
    }

    return;

  }

  private resolveValueList(d: MinTransformData, utr: UtrData): ValueData<string> | undefined {
    if (!d.value) {
      return undefined;
    }

    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return undefined;
    }

    const option = this.resolveOption(options, d.value, utr.valueValidation?.valueList?.allowCustomOptions);

    if (option) {
      return {
        data: option.code,
        input: { data: option.code, unit: undefined, numberScale: undefined }
      };
    }
    return undefined;
  }

  private resolveValueListMulti(d: MinTransformData, utr: UtrData) {
    const value = d.value as (undefined | string | number | string[]);
    if (value === undefined || value === null) {
      return undefined;
    }

    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return undefined;
    }

    const valueArray = getValueArray(value);

    const result = valueArray.reduce((a, code) => {
      // Code takes priority
      const option = this.resolveOption(options, code, utr.valueValidation?.valueList?.allowCustomOptions);
      if (option) {
        a.push(option.code);

      }
      return a;
    }, <string[]>[]);


    if (result.length > 0) {
      return {
        data: result,
        input: { data: result, unit: undefined, numberScale: undefined },
      };
    }
    return undefined;
  }

  private createInputData(update: UtrvUpdate & ObjectData ): UtrvUpdateInputData & ObjectData {
      if (!update.valueData.input) {
        update.valueData.input = {
          unit: undefined,
          numberScale: undefined,
        };
        if (!update.valueData.input.data) {
          update.valueData.input.data = {};
        }
      }
      return update as UtrvUpdateInputData & ObjectData;
  }
  
  private isSameInputUnitNumberScaleNumericValueList(d: MinTransformData, importData: MinTransformData[] = []) {
    return importData
      .filter((r) => r.utrCode === d.utrCode)
      .every((r) => r.unit === d.unit && r.numberScale === d.numberScale);
  }

  private resolveComplexValueList({
    row,
    utr,
    importData,
    update,
    currency,
  }: {
    row: MinTransformData;
    utr: UtrData;
    importData: MinTransformData[];
    update?: UtrvUpdate;
    currency: string;
  }): ValueData | undefined {
    if (!row.columnCode) {
      return update?.valueData;
    }

    const optionValue = typeof row.value === 'string' ? row.value.trim() : row.value;
    if (optionValue === '' || optionValue === undefined) {
      return update?.valueData;
    }

    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return undefined;
    }

    // Code takes priority
    const option = this.resolveOption(options, row.columnCode, utr.valueValidation?.valueList?.allowCustomOptions);
    if (!option) {
      return undefined;
    }

    const { inputUnit, defaultUnit, inputNumberScale, defaultNumberScale, inputValue, convertedValue, isCurrency } =
    this.convertUnitNumberScaleData({ data: row, utr, currency });
    const isSameInputUnitNumberScale = this.isSameInputUnitNumberScaleNumericValueList(row, importData);

    if (this.isExistingData(update)) {
      const updateInput = this.createInputData(update);
      updateInput.valueData.data[option.code] = row.value;
      updateInput.valueData.input.data[option.code] = row.value;

      if (utr.valueType === UtrValueType.NumericValueList) {
        updateInput.valueData.data[option.code] = convertedValue;
        updateInput.valueData.input.data[option.code] = isSameInputUnitNumberScale ? inputValue : convertedValue;
        updateInput.valueData.input.unit = isSameInputUnitNumberScale || isCurrency ? inputUnit : defaultUnit;
        updateInput.valueData.input.numberScale = isSameInputUnitNumberScale ? inputNumberScale : defaultNumberScale;
        updateInput.valueData.input.value = this.calculateInputValueNumericValueList(updateInput);
      }
      return updateInput.valueData;
    }

    // Create new
    const isNumericValueListType = utr.valueType === UtrValueType.NumericValueList && !isNaN(row.value as number);
    const valueOfNumericValueList = isSameInputUnitNumberScale ? inputValue : convertedValue;
    const unitOfNumericValueList = isSameInputUnitNumberScale || isCurrency ? inputUnit : defaultUnit;
    const numberScaleOfNumericValueList = isSameInputUnitNumberScale ? inputNumberScale : defaultNumberScale;
    const data = { [option.code]: isNumericValueListType ? convertedValue : row.value };
    const inputData = { [option.code]: isNumericValueListType ? valueOfNumericValueList : row.value };
    return {
      data,
      input: {
        data: inputData,
        unit: isNumericValueListType ? unitOfNumericValueList : undefined,
        value: isNumericValueListType ? Number(valueOfNumericValueList) : undefined,
        numberScale: isNumericValueListType ? numberScaleOfNumericValueList : undefined,
      },
    };
  }

  private isExistingData(update?: UtrvUpdate): update is UtrvUpdate & ObjectData {
    return typeof update?.valueData?.data === 'object';
  }

  private convertExcelDateToJSDate(serial: number) {
    const utc_days = Math.floor(serial - 25569);
    const utc_value = utc_days * 86400;
    return new Date(utc_value * 1000);
  }

  private convertUnitNumberScaleData({
    data,
    utr,
    currency,
  }: {
    data: MinTransformData;
    utr: DestinationConversion;
    currency?: string;
  }) {
    const isCurrency = utr.unitType === SupportedMeasureUnits.currency;
    const inputUnit = MetricUnitManager.getUnitCode(data.unit, utr.unitType, currency);
    const inputNumberScale = this.getValidNumberScaleData(data);
    const inputValue = typeof data.value === 'string' && data.value.trim() === '' ? undefined : data.value;

    const convertedValue =
      inputValue && (inputUnit || inputNumberScale)
        ? MetricUnitManager.convertUnitNumberScale({
            value: Number(inputValue),
            from: {
              unit: !isCurrency ? inputUnit : undefined,
              numberScale: inputNumberScale,
            },
            to: {
              unit: !isCurrency ? utr.unit : undefined,
              numberScale: utr.numberScale,
            },
          })
        : inputValue;

    return {
      inputUnit,
      inputNumberScale,
      // always be default utr unit even for currency utrs
      defaultUnit: utr.unit,
      defaultNumberScale: utr.numberScale,
      inputValue,
      convertedValue,
      isCurrency,
    };
  }
  
  private getValidNumberScaleData(d: MinTransformData) {
    return isCorrectNumberScaleCode(d.numberScale) ? d.numberScale : undefined;
  }

  private resolveTable({
    row,
    utr,
    update,
    currency,
  }: {
    row: MinTransformData;
    utr: UtrData;
    update?: UtrvUpdate & { currentRow?: number };
    currency: string;
  }) {
    let value: string | number | undefined | string[] = row.value;
    const optionValue = typeof value === 'string' ? value.trim() : value;

    const values = row.values || [];

    // We no longer skip the empty values, to preserver the multi table
    // "Value 1, Value 2, Value n+1" setup, otherwise rows will get out of sync.
    // @TODO write separate logic for multi row table resolve logic without flattening
    const allValuesEmpty = values.every((v) => {
      return v === undefined || (typeof v === 'string' && v.trim() === '');
    });

    if ((optionValue === '' || optionValue === undefined) && allValuesEmpty) {
      return update?.valueData;
    }

    const column = this.getTableColumnCode(row, utr);
    if (!column) {
      return update?.valueData;
    }

    if (column.type === ColumnType.Date) {
      let date;
      if (typeof optionValue === 'number') {
        date = this.convertExcelDateToJSDate(optionValue);
        value = date.toISOString().split('T')[0];
      } else if (optionValue !== undefined) {
        date = new Date(optionValue);
        value = date.toISOString().split('T')[0];
      }
    }

    const options = column.options;
    if (options && options.length > 0) {
      if (typeof optionValue !== 'string') {
        return update?.valueData;
      }

      const values = optionValue.split(','); // Deal with multi-select
      const codes = values
        .map(v => v.trim())
        .map(v => {
          const option = this.resolveOption(options, v, column.validation?.allowCustomOptions)
          return option ? option.code : '';
        })
        .filter(v => v !== '');

      value = codes.length > 1 ? codes : codes[0];
      if (value === undefined) {
        return update?.valueData;
      }
    }

    const columnCode = column.code;
    let valueTable = value, unit, numberScale;
    if (column.type === ColumnType.Number) {
      const { inputUnit, inputNumberScale, convertedValue } = this.convertUnitNumberScaleData({
        data: row,
        utr: column as DestinationConversion,
        currency,
      });
      valueTable = convertedValue ?? value;
      unit = inputUnit;
      numberScale = inputNumberScale;
    }

    if (!update?.valueData?.table || !update.valueData.input?.table) {
      const initialValueTable = [[{ code: columnCode, value: valueTable }]];
      const initialInputValueTable: InputTableData = [[{ code: columnCode, value, unit, numberScale }]];
      return {
        table: initialValueTable,
        input: {
          table: initialInputValueTable,
          unit: undefined,
          numberScale: undefined,
        },
      };
    }

    const table: TableData = update.valueData.table;
    const inputTable: InputTableData = update.valueData.input.table;
    const tableRow = table[update.currentRow ?? 0];
    const inputTableRow = inputTable[update.currentRow ?? 0];

    if (tableRow) {
      const newInputRecord: InputRowData = { code: columnCode, value, unit, numberScale };
      const newRecord = { code: columnCode, value: valueTable };
      const col = tableRow.find((c) => c.code === columnCode);
      if (!col) {
        // New column can add to existing row
        tableRow.push(newRecord);
        inputTableRow.push(newInputRecord);
        return update.valueData;
      }
      // Create new row and add
      update.currentRow = table.push([newRecord]) - 1;
      inputTable.push([newInputRecord]);
    }

    return update.valueData;
  }

  private getTableColumnCode(row: MinTransformData, utr: Pick<UtrData, 'valueValidation'>) {
    if (!row.columnCode && !row.columnName) {
      return undefined;
    }
    return utr.valueValidation?.table?.columns.find(c => {
      return c.code === row.columnCode || c.name === row.columnName;
    });
  }

  private getTableCalculationColumns(utr: UtrData) {
    const columns = utr.valueValidation?.table?.columns;
    if (!columns || columns.length === 0) {
      return;
    }
    return columns.filter(col => col.calculation);
  }

  private getValueList(utrs: UtrData[]) {
    const ids: ObjectId[] = [];
    utrs.forEach(u => {
      ids.push(...extractListId(u.valueValidation));
    });
    return ValueListRepository.findByIds(ids);
  }

  /**
   * Convert utrvs to rows format
   */
  private convertToRows(params: ConvertToRowsParams): QuestionRow[] {
    const { utr, utrv, valueListMap, preferredTypes, utrOverridesMap } = params;
    switch (utr.valueType) {
      case UtrValueType.Number:
      case UtrValueType.Sample:
      case UtrValueType.Percentage: {
        const decimal = utrOverridesMap?.get(utr._id.toString())?.valueValidation?.decimal;
        const cellValue = getCellValueWithFormat(utrv.value, decimal);
        return [
          {
            utrId: utr._id.toString(),
            utrvId: String(utrv._id),
            utrCode: utr.code,
            typeCode: getPreferredTypeCode(utr, preferredTypes),
            question: getPreferredValueLabel(utr, preferredTypes),
            value: cellValue,
            valueType: utr.valueType,
            note: utrv.note,
            notApplicableType: utrv.valueData?.notApplicableType,
            unit: utr.unit,
            numberScale: utr.numberScale,
            effectiveDate: utrv.effectiveDate,
            type: utrv.type,
            isImportVerified: this.isUtrvImportVerified(utrv),
            unitType: utr.unitType,
          },
        ];
      }
      case UtrValueType.Text:
      case UtrValueType.Date:
        return [
          {
            utrId: utr._id.toString(),
            utrvId: String(utrv._id),
            utrCode: utr.code,
            typeCode: getPreferredTypeCode(utr, preferredTypes),
            question: getPreferredValueLabel(utr, preferredTypes),
            value: utrv.valueData?.data,
            valueType: utr.valueType,
            note: utrv.note,
            notApplicableType: utrv.valueData?.notApplicableType,
            effectiveDate: utrv.effectiveDate,
            type: utrv.type,
            isImportVerified: this.isUtrvImportVerified(utrv),
          },
        ];
      case UtrValueType.ValueList:
      case UtrValueType.ValueListMulti:
        return this.transformValueList(utrv, utr, preferredTypes)
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList:
        return this.transformComplexValueList({ utrv, utr, preferredTypes, utrOverridesMap });
      case UtrValueType.Table:
      return this.transformTable({ utrv, utr, valueListMap, preferredTypes, utrOverridesMap })
      default:
        return []
    }
  }

  private transformValueList(utrv: UniversalTrackerValuePlain, utr: UtrData, preferredTypes: string[]): QuestionRow[] {

    const options = this.getValueListOptions(utr) || []
    const data = utrv.valueData?.data;

    const row: QuestionRow = {
      utrId: utr._id.toString(),
      utrvId: utrv._id.toString(),
      utrCode: utr.code,
      typeCode: getPreferredTypeCode(utr, preferredTypes),
      question: getPreferredValueLabel(utr, preferredTypes),
      value: undefined,
      valueType: utr.valueType,
      notApplicableType: utrv.valueData?.notApplicableType,
      note: utrv.note,
      options,
      optionsListId: utr.valueValidation?.valueList?.listId,
      effectiveDate: utrv.effectiveDate,
      type: utrv.type,
      isImportVerified: this.isUtrvImportVerified(utrv),
    };

    if (!data) {
      return [row];
    }

    if (Array.isArray(data)) {
      row.value = data
        .map((value) => this.getOptionName(options, value))
        .filter((e) => e)
        .join(', ');
    } else if (typeof data === 'string') {
      row.value = this.getOptionName(options, data);
    }

    return [row];
  }

  private getOptionName(options: Option[], value: any) {
    return options.find(({ code }) => value === code)?.name
  }

  private transformComplexValueList({
    utrv,
    utr,
    preferredTypes,
    utrOverridesMap,
  }: {
    utrv: UniversalTrackerValuePlain;
    utr: UtrData;
    preferredTypes: string[];
    utrOverridesMap?: UtrOverridesMap;
  }): QuestionRow[] {
    const options = this.getValueListOptions(utr);
    if (!Array.isArray(options)) {
      return [];
    }
    const decimal = utrOverridesMap?.get(utr._id.toString())?.valueValidation?.decimal;

    return options.map((option, i) => {
      const rawValue = utrv.valueData?.data?.[option.code] as Value;
      const cellValue =
        utr.valueType === UtrValueType.TextValueList ? rawValue : getCellValueWithFormat(rawValue, decimal);

      return {
        utrId: utr._id.toString(),
        utrvId: utrv._id.toString(),
        utrCode: utr.code,
        typeCode: getPreferredTypeCode(utr, preferredTypes),
        question: getPreferredValueLabel(utr, preferredTypes),
        columnCode: option.code,
        columnName: option.name,
        value: cellValue,
        valueType: utr.valueType,
        notApplicableType: utrv.valueData?.notApplicableType,
        note: i === 0 ? utrv.note : undefined,
        unit: utr.unit,
        numberScale: utr.numberScale,
        effectiveDate: utrv.effectiveDate,
        type: utrv.type,
        isImportVerified: this.isUtrvImportVerified(utrv),
        unitType: utr.unitType,
      };
    });
  }

  private transformTable({
    utrv,
    utr,
    valueListMap,
    preferredTypes,
    utrOverridesMap,
  }: {
    utrv: UniversalTrackerValuePlain;
    utr: UtrData;
    valueListMap: Map<string, Option[]>;
    preferredTypes: string[];
    utrOverridesMap?: UtrOverridesMap;
  }) {
    const columns = utr.valueValidation?.table?.columns ?? [];
    const columnsWithoutCalculation = columns.filter((column) => !column.calculation);
    const rowData: QuestionRow[] = [];
    if (!columns) {
      return rowData;
    }

    const table = utrv.valueData?.table;

    // Ensure we have at least one row, to populate export sheet
    const tableData = !Array.isArray(table) || table.length <= 0 ? [[]] : table;

    rowData.push(
      ...columnsWithoutCalculation.map((tableColumn, i) => {
        const options = tableColumn.listId ? valueListMap.get(String(tableColumn.listId)) : undefined;
        const values = this.getTableColumnInputs({
          utrId: utr._id.toString(),
          columnCode: tableColumn.code,
          tableData,
          options,
          utrOverridesMap,
        });

        return {
          utrId: utr._id.toString(),
          utrvId: utrv._id.toString(),
          utrCode: utr.code,
          typeCode: getPreferredTypeCode(utr, preferredTypes),
          question: getPreferredValueLabel(utr, preferredTypes),
          columnCode: tableColumn.code,
          columnName: tableColumn.name,
          valueType: tableColumn.type,
          value: undefined,
          // Table use values to generate Value 1, Value 2 columns...
          values,
          notApplicableType: utrv.valueData?.notApplicableType,
          note: i === 0 ? utrv.note : undefined,
          unit: tableColumn.unit,
          numberScale: tableColumn.numberScale,
          effectiveDate: utrv.effectiveDate,
          type: utrv.type,
          isImportVerified: this.isUtrvImportVerified(utrv),
          unitType: tableColumn.unitType,
          options,
          optionsListId: tableColumn.listId,
        };
      })
    );

    return rowData;
  }

  /**
   * Table must preserve values to ensure layout is not mix
   *       Input 1,   Input 2,   Input 3
   * col1  name-row1  name-row2  name-row3
   * col2  10,        '',        '12.351'
   * col3  undefined  30         30.25
   *
   * That means when we are looking for columnCode=2 we need to ensure we return
   * [10, "", "12.351"] and not [10, "12.351"] as that will break the layout.
   * and will make "12.351" to be imported as part of row2, which is not correct.
   *
   *
   * @TODO these should actually be FormattedCell, instead of getFormattedValue
   * This deviates from other types, that return FormattedCell and allow xlsx to
   * apply formatting. This should be updated to be consistent.
   */
  private getTableColumnInputs({
    utrId,
    options,
    utrOverridesMap,
    tableData,
    columnCode,
  }: TableInputParams) {
    const columnInputs = tableData.map((row) => {
      const col = row.find((r) => r.code === columnCode);
      // column value is plain text, number or date
      if (!options) {
        return col?.value;
      }
      // column value has valueList options
      if (col?.value && Array.isArray(col.value)) {
        return col.value.map(value => options.find(({ code }) => value === code)?.name || col?.value).join(', ');
      }
      return options.find(({ code }) => col?.value === code)?.name || col?.value;
    });

    const decimal = utrOverridesMap?.get(utrId)?.valueValidation?.table?.columns?.find((col) => col.code === columnCode)
      ?.validation?.decimal;

    return columnInputs.map((colInput) => getFormattedValue(colInput, decimal));
  }

  private shouldSkipUpdate(utr: UtrData, update: UtrvUpdate) {

    const valueData = update.valueData;
    if (valueData?.notApplicableType) {
      return false;
    }

    switch (utr.valueType) {
      case UtrValueType.Text:
      case UtrValueType.Date:
        return valueData?.data === undefined || (typeof valueData?.data === 'string' && valueData.data.trim() === '');
      case UtrValueType.ValueList:
      case UtrValueType.ValueListMulti:
        return valueData?.data === undefined;
      case UtrValueType.NumericValueList:
      case UtrValueType.TextValueList:
        return typeof valueData?.data !== 'object' || Object.keys(valueData.data ?? {}).length == 0;
      case UtrValueType.Table:
        return valueData?.table === undefined
      case UtrValueType.Number:
      case UtrValueType.Sample:
      case UtrValueType.Percentage:
      default:
        return update.value === undefined
    }
  }

  private getEvidenceLinks(data: MinTransformData): LinkDocument[] {
    const external = this.mapLinks(data.externalEvidenceLinks, true);
    const internal = this.mapLinks(data.internalEvidenceLinks, false);

    return [...external, ...internal];
  }

  private mapLinks(links: string[] | undefined, isPublic: boolean) {
    return (links || []).map((link) => ({
      link,
      public: isPublic,
    }));
  }

  private isUtrvImportVerified(utrv: UniversalTrackerValuePlain) {
    return utrv.status === ActionList.Verified && !!utrv.valueData?.isImported;
  }
}

let instance: DataTransform;
export const getDataTransform = () => {
  if (!instance) {
    instance = new DataTransform(wwgLogger);
  }
  return instance;
}
