/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Blueprint, SurveyForm } from '../../repository/BlueprintRepository';
import UniversalTrackerValue, {
  CompositeData,
  UniversalTrackerValueModel,
  UniversalTrackerValuePlain,
} from '../../models/universalTrackerValue';
import { SurveyModel } from '../../models/survey';
import { Document } from 'mongoose';
import { ObjectId } from 'bson';
import UniversalTrackerActionManager from '../utr/UniversalTrackerActionManager';
import { UTrGroupConfigInterface } from '../../survey/utrGroupConfigs';
import UniversalTracker from '../../models/universalTracker';
import { CompositeUtrConfigInterface } from '../../survey/compositeUtrConfigs';
import { UserPlain } from '../../models/user';
import { wwgLogger } from '../wwgLogger';
import * as utrvUtil from '../utr/utrvUtil';
import { getCompositeData } from '../utr/utrvUtil';
import { extractUtrCodes, extractVisibleUtrCodes } from '../../survey/surveyForms';
import {
  MinUtrData,
  SurveyProcessRepository,
  SurveyProcessUtrvPlain,
  minUtrDataProject,
} from '../../repository/SurveyProcessRepository';
import { ActionList } from '../utr/constants';
import { StakeholderGroupManager } from '../stakeholder/StakeholderGroupManager';
import { activeBlueprints, Blueprints } from '../../survey/blueprints';
import { ClonedData, CloneOptions } from './model/ProcessData';
import { SourceTypes, StakeholderGroup } from '../../models/public/universalTrackerValueType';
import { getBluePrintContribution } from './BlueprintContribution';
import { UtrConfig } from '../../rules/UtrConfig';

const upgradableSourceTypes = [SourceTypes.Fragment, SourceTypes.SubFragment];

export type UtrvProcessData = SurveyProcessUtrvPlain | UniversalTrackerValueModel;

export class SurveyProcess {

  private universalTrackerCache = new Map<string, MinUtrData>();
  private utrvCloneMap: Map<string, ClonedData> = new Map();
  private utrvMap: Map<string, UtrvProcessData> = new Map();
  private disabledUtrvs: Set<string> = new Set();
  private compositeUtrvs: Set<string> = new Set();
  private fragmentUtrvs: Set<string> = new Set();
  private subFragmentUtrvs: Set<string> = new Set();
  private visibleUtrvs: Set<string> = new Set();
  private visibleStakeholders: Set<string> = new Set();
  private cloneOptions: CloneOptions | undefined;

  public constructor(
    private surveyForms: Blueprint,
    protected user: UserPlain,
    protected surveyModel: SurveyModel,
    private blueprintContribution = getBluePrintContribution(),
  ) {
  }

  public async regenerateSurvey() {
    // Load current
    await this.loadCurrentData();
    return this.generateSurvey();
  }

  public async clone(dataToClone: ClonedData[], options: CloneOptions) {

    if (!this.surveyModel.isNew) {
      throw new Error(`Tried to clone data to existing survey ${this.surveyModel._id}`)
    }

    if (dataToClone.some(v => !v.isCloned)) {
      throw new Error(`Data required to be marked as cloned`);
    }

    dataToClone.forEach(data => {
      if (!data.universalTracker) {
        throw new Error(`Clone data: missing universalTracker, initiativeId: ${data.initiativeId}`);
      }
      const uniqueCode = this.getUniqueUtrvCode(data.universalTracker.code);
      if (this.utrvCloneMap.has(uniqueCode)) {
        throw new Error(`Duplicate clone entry ${uniqueCode}`)
      }
      this.utrvCloneMap.set(uniqueCode, data);
    });

    this.cloneOptions = options;

    return this.generateSurvey();
  }

  public async generateSurvey() {

    // Clean up old references
    if (this.surveyForms.references.length === 0) {
      this.surveyModel.references = [];
    }

    // Clear visible, will be re-created
    this.surveyModel.visibleUtrvs = [];

    await this.loadRelatedUtrvValues();

    const configs = this.getMergedConfig();

    for (const surveyForm of configs) {
      await this.createSurveyForm(surveyForm);
    }

    // Remove Not contributing questions
    const contribution = await this.blueprintContribution.getContributions(this.surveyModel.sourceName as Blueprints);
    const currentCodes = extractVisibleUtrCodes(this.surveyForms);
    const remainingSdgCodes = currentCodes.reduce((a, visibleCode) => {
      a.add(visibleCode);
      contribution[visibleCode]?.forEach(code => a.add(code))
      return a;
    }, new Set<string>());

    for (const [code, v] of this.utrvMap.entries()) {
      if (!remainingSdgCodes.has(code)) {
        const utrvId = String(v._id);
        this.compositeUtrvs.delete(utrvId);
        this.fragmentUtrvs.delete(utrvId);
        this.subFragmentUtrvs.delete(utrvId);
        this.disabledUtrvs.add(utrvId);
      }
    }

    this.disabledUtrvs.forEach((utrv) => this.surveyModel.disabledUtrvs.push(new ObjectId(utrv)));
    this.compositeUtrvs.forEach((utrv) => this.surveyModel.compositeUtrvs.push(new ObjectId(utrv)));
    this.fragmentUtrvs.forEach((utrv) => this.surveyModel.fragmentUtrvs.push(new ObjectId(utrv)));
    this.subFragmentUtrvs.forEach((utrv) => this.surveyModel.subFragmentUtrvs.push(new ObjectId(utrv)));

    const uniqueStakeholders = new Set(this.surveyModel.visibleStakeholders.map(String));
    this.visibleStakeholders.forEach((_id) => uniqueStakeholders.add(_id));
    this.surveyModel.visibleStakeholders = Array.from(uniqueStakeholders).map(id => new ObjectId(id));

    this.visibleUtrvs.forEach((_id) => this.surveyModel.visibleUtrvs.push(new ObjectId(_id)));
    return this.utrvMap;
  }

  public getDisabled() {
    return this.disabledUtrvs;
  }

  private async createSurveyForm(surveyForm: SurveyForm) {

    if (surveyForm.utrGroupConfig) {
      // Generic questions
      return this.createQuestionGroup(surveyForm);
    }

    const compositeConfig = <CompositeUtrConfigInterface>surveyForm.config;
    if (!compositeConfig) {
      throw new Error(`Could not load config for code ${surveyForm.compositeConfig}`);
    }
    const { compositeUtrCode } = compositeConfig;

    const fragmentUtrCodes = UtrConfig.getFragmentUtrCodes(compositeConfig);
    try {
      const questionGroupUtrv = await this.createUtrv(
        compositeUtrCode,
        this.getUniqueUtrvCode(compositeUtrCode),
        SourceTypes.Composite,
        surveyForm
      );

      for (const questionCode of fragmentUtrCodes) {
        await this.createQuestionUtrv(questionCode, surveyForm, questionGroupUtrv)
      }
    } catch (e) {
      wwgLogger.warn(`Generating survey with invalid UTR code: ${compositeUtrCode}`);
    }
  }

  public async saveUpdate() {
    const values: Document[] = [];
    const updates: any[] = [];

    // Add everything to save array
    this.utrvMap.forEach((model) => {
      if ('save' in model) {
        values.push(model);
      } else {
        updates.push({
          updateOne: {
            filter: { _id: model._id },
            update: this.prepareUpdate(model)
          }
        });
      }
    });
    if (updates.length) {
      await UniversalTrackerValue.bulkWrite(updates)
    }

    if (values.length > 0) {
      await UniversalTrackerValue.bulkSave(values);
    }

    await this.surveyModel.save();

    await this.softDeleteDisabledUtrvs();

    return this.surveyModel;
  }

  private prepareUpdate(model: SurveyProcessUtrvPlain) {
    // Deal with undefined mongoose way, by un-setting them manually
    const update: any = { $set: model, $unset: {} };
    const { $set, $unset } = update;

    for (const prop of Object.keys($set)) {
      if ($set[prop] === undefined) {
        delete $set[prop];
        $unset[prop] = 1;
      }
    }
    return update;
  }

  private softDeleteDisabledUtrvs() {
    if (this.surveyModel.disabledUtrvs.length <= 0) {
      return;
    }
    const ids = this.surveyModel.disabledUtrvs.map(id => new ObjectId(id));
    return UniversalTrackerValue.updateMany(
      { _id: { $in: ids } },
      { $set: { deletedDate: new Date() } },
      { multi: true },
    ).exec().catch(wwgLogger.error);
  }

  private async createQuestionUtrv(
    questionCode: string,
    surveyForm: SurveyForm,
    questionGroupUtrv: UtrvProcessData
  ) {
    const questionUtrv = await this.createUtrv(
      questionCode,
      this.getUniqueUtrvCode(questionCode),
      SourceTypes.Fragment,
      surveyForm
    );

    this.addFragmentUtrvIdToCompositeData(questionGroupUtrv, questionUtrv._id, surveyForm);
    return questionUtrv;
  }

  private getMergedConfig() {
    let configs = this.surveyForms.forms;
    if (this.surveyForms.additionalConfigs && this.surveyForms.additionalConfigs.length > 0) {
      configs = [...this.surveyForms.additionalConfigs, ...configs];
    }
    return configs;
  }

  private async createQuestionGroup(surveyForm: SurveyForm) {
    const questionGroupConfig = <UTrGroupConfigInterface>surveyForm.config;
    if (!questionGroupConfig) {
      return;
    }

    const { utrCodes } = questionGroupConfig;
    for (const questionCode of utrCodes) {
      try {
        const questionUtrv = await this.createUtrv(
          questionCode,
          this.getUniqueUtrvCode(questionCode),
          SourceTypes.Fragment,
          surveyForm,
        );

        // Question groups is only visible part
        this.visibleUtrvs.add(String(questionUtrv._id));
        const { stakeholder, verifier } = questionUtrv.stakeholders as StakeholderGroup;
        [...stakeholder, ...verifier]
          .map(String)
          .forEach(id => this.visibleStakeholders.add(id))
      } catch (e) {
        wwgLogger.warn(`Generating survey with invalid UTR code: ${questionCode}`);
      }
    }
  }

  private getUniqueUtrvCode(questionCode: string) {
    return `${questionCode}`;
  }

  protected async createUtrv(
    utrCode: string,
    uniqueUtrvCode: string,
    sourceType: string,
    surveyForm: SurveyForm,
    nodeId?: ObjectId,
  ) {
    const existingUtrv = this.utrvMap.get(uniqueUtrvCode);
    if (existingUtrv) {
      return this.updateUtrv(surveyForm, existingUtrv, sourceType, uniqueUtrvCode);
    }

    const utr = await this.getUtr(utrCode);
    const { _id: universalTrackerId } = utr;
    const sourceCode = surveyForm && surveyForm.compositeConfig ? surveyForm.compositeConfig : '';

    const utrv = new UniversalTrackerValue();
    utrv.universalTrackerId = universalTrackerId;
    utrv.initiativeId = nodeId || this.surveyModel.initiativeId;
    utrv.sourceType = sourceType;
    utrv.sourceCode = sourceCode;
    utrv.period = this.surveyModel.period;
    utrv.effectiveDate = this.surveyModel.effectiveDate;
    utrv.type = this.surveyModel.utrvType;
    utrv.evidenceRequired = sourceType === SourceTypes.Composite ? false : this.surveyModel.evidenceRequired;
    utrv.noteRequired = sourceType === SourceTypes.Composite ? false : this.surveyModel.noteRequired;
    utrv.verificationRequired = sourceType === SourceTypes.Composite ? false : this.surveyModel.verificationRequired;
    utrv.stakeholders = this.surveyModel.stakeholders;
    utrv.escalationPolicy = this.surveyModel.escalationPolicy;
    this.createCompositeData(utrv, sourceCode);

    const clonedData = this.utrvCloneMap.get(uniqueUtrvCode);
    if (this.isCloningData(clonedData)) {
      const { value, valueData, note, numberScale, unit } = clonedData;
      UniversalTrackerActionManager.hydrateUpdate({
        utrv,
        userId: this.user._id,
        value,
        note,
        valueData,
        numberScale,
        unit
      });
    } else {
      UniversalTrackerActionManager.initialize(utrv, this.user._id);
    }

    if (this.isCloningDelegation(clonedData)) {
      utrv.stakeholders = StakeholderGroupManager.mergeGroup(StakeholderGroupManager.getEmptyGroup(), {
        add: clonedData.stakeholders as StakeholderGroup,
        remove: StakeholderGroupManager.getEmptyGroup(),
      })
    }

    this.utrvMap.set(uniqueUtrvCode, utrv);
    this.pushToSurveyModel(sourceType, utrv._id.toString(), surveyForm);

    return utrv;
  }

  private isCloningData(clonedData?: ClonedData): clonedData is ClonedData {
    // Not cloning data for created
    if (!clonedData || clonedData.status === ActionList.Created) {
      return false;
    }
    return Boolean(this.cloneOptions?.hasData);
  }

  private isCloningDelegation(clonedData?: ClonedData): clonedData is ClonedData {
    return Boolean(clonedData && this.cloneOptions?.delegation);
  }

  private async updateUtrv(
    surveyForm: SurveyForm,
    utrv: UtrvProcessData,
    sourceType: string,
    uniqueUtrvCode: string
  ) {

    // Don't add to if form have different owner
    if (this.configHasDifferentOwner(surveyForm)) {
      this.disabledUtrvs.delete(utrv._id.toString());
      return utrv;
    }

    // From utrGroup, must have stakeholder. Skip cloned
    if (surveyForm.utrGroupConfig && !this.isCloningDelegation(this.utrvCloneMap.get(uniqueUtrvCode))) {
      this.mergeStakeholders(utrv);
    }

    const configCode = surveyForm && surveyForm.compositeConfig ? surveyForm.compositeConfig : '';
    if (this.shouldUpgradeToComposite(utrv, sourceType)) {
      // Overwrite in case new one is type Composite, as it takes priority
      this.createCompositeData(utrv, configCode);
    }

    this.setConfigCode(utrv, configCode);
    utrv.sourceType = sourceType;
    utrv.sourceCode = configCode;
    utrv.evidenceRequired = this.surveyModel.evidenceRequired;
    utrv.noteRequired = this.surveyModel.noteRequired;
    utrv.verificationRequired = this.surveyModel.verificationRequired;

    if (!utrv.escalationPolicy && !utrvUtil.isComposite(utrv, this.surveyModel.sourceName)) {
      utrv.escalationPolicy = this.surveyModel.escalationPolicy;
    }
    this.pushToSurveyModel(sourceType, utrv._id.toString(), surveyForm);

    return utrv;
  }

  private mergeStakeholders(utrv: UtrvProcessData) {
    utrv.stakeholders = StakeholderGroupManager.mergeGroup(
      utrv.stakeholders as StakeholderGroup,
      {
        add: this.surveyModel.stakeholders,
        remove: {
          stakeholder: [],
          verifier: [],
          escalation: []
        },
      }
    );
  }

  private pushToSurveyModel(sourceType: string, utrvId: string, surveyForm: SurveyForm) {

    this.disabledUtrvs.delete(utrvId);

    // Don't add to survey if form have different owner
    if (this.configHasDifferentOwner(surveyForm)) {
      return;
    }

    if (sourceType === SourceTypes.Composite) {
      this.compositeUtrvs.add(utrvId);
      this.fragmentUtrvs.delete(utrvId);
      this.subFragmentUtrvs.delete(utrvId);
    } else if (sourceType === SourceTypes.Fragment && !this.compositeUtrvs.has(utrvId)) {
      this.fragmentUtrvs.add(utrvId);
    } else if (sourceType === SourceTypes.SubFragment) {
      this.subFragmentUtrvs.add(utrvId);
    }
  }

  private async getUtr(code: string) {

    const cached = this.universalTrackerCache.get(code);
    if (cached) {
      return cached;
    }

    const utr = await UniversalTracker.findOne({ 'code': code }, minUtrDataProject).lean().exec();
    if (!utr) {
      throw new Error(`No UTR found for code ${code}`);
    }

    this.universalTrackerCache.set(code, utr);
    return utr;
  }

  private createCompositeData(utrv: UtrvProcessData, configCode?: string) {
    const comp = getCompositeData(utrv);
    if (!comp) {
      utrv.compositeData = this.createEmptyCompositeData(configCode);
      return;
    }

    if (comp.blueprint && comp.blueprint !== this.surveyModel.sourceName) {
      return;
    }

    const { secondary } = comp;
    utrv.compositeData = { ...this.createEmptyCompositeData(configCode), secondary };
  }

  private shouldUpgradeToComposite(utrv: UtrvProcessData, sourceType: string) {
    return sourceType === SourceTypes.Composite
      && upgradableSourceTypes.includes(utrv.sourceType as SourceTypes);
  }

  protected async loadCurrentData() {
    const data = await SurveyProcessRepository.loadSurveyUtrvs(this.surveyModel);

    this.utrvMap.clear();
    data.forEach(utrv => {
      if (!utrv.universalTracker[0]) {
        // This is commonly caused by deleted custom metrics
        wwgLogger.info(`Failed to lookup universalTracker for id ${utrv.universalTrackerId} utrv: _id ${utrv._id}`);
        return;
      }

      const utrCode = utrv.universalTracker[0].code;
      this.universalTrackerCache.set(utrCode, utrv.universalTracker[0]);
      this.disabledUtrvs.add(utrv._id.toString());

      const uniqueCode = this.getUniqueUtrvCode(utrCode);

      const existingUtrv = this.utrvMap.get(uniqueCode);

      // Existing one is from earlier date
      if (existingUtrv?.created && existingUtrv.created < (utrv.created as Date)) {
        return;
      }
      const { universalTracker, ...universalTrackerValueModel } = utrv;

      let configCode;
      if (universalTrackerValueModel.compositeData) {
        configCode = universalTrackerValueModel.compositeData.configCode;
      }

      this.createCompositeData(universalTrackerValueModel, configCode);
      universalTrackerValueModel.deletedDate = undefined;

      this.utrvMap.set(uniqueCode, universalTrackerValueModel);
    });

    this.surveyModel.compositeUtrvs.forEach((utrvId) => this.disabledUtrvs.add(utrvId.toString()));
    this.surveyModel.fragmentUtrvs.forEach((utrvId) => this.disabledUtrvs.add(utrvId.toString()));
    this.surveyModel.subFragmentUtrvs.forEach((utrvId) => this.disabledUtrvs.add(utrvId.toString()));
    this.surveyModel.disabledUtrvs.forEach((utrvId) => this.disabledUtrvs.add(utrvId.toString()));

    this.surveyModel.compositeUtrvs = [];
    this.surveyModel.fragmentUtrvs = [];
    this.surveyModel.subFragmentUtrvs = [];
    this.surveyModel.disabledUtrvs = [];
  }

  private addFragmentUtrvIdToCompositeData(
    utrv: UtrvProcessData,
    _id: ObjectId,
    surveyForm: SurveyForm
  ) {
    const newId = _id.toString();

    const compData = getCompositeData(utrv);
    const { sourceName } = this.surveyModel;
    const sourceCode = surveyForm ? surveyForm.compositeConfig : '';

    if (!compData) {
      throw new Error(`Failed to find compositeData for ${utrv._id}, "${sourceName}" code`);
    }

    // Are we dealing with different sourceName owner configuration
    if (this.configHasDifferentOwner(surveyForm)) {

      // Add empty secondary compositeData
      if (!Array.isArray(compData.secondary)) {
        compData.secondary = [];
      }

      const compositeData = getCompositeData(utrv, sourceName);
      if (!compositeData) {
        compData.secondary.push(this.createEmptyCompositeData(sourceCode));
      }

      const secondaryCompData = getCompositeData(utrv, sourceName) as CompositeData;
      // push fragmentId to borrowed/link utrv composite data
      if (!secondaryCompData.fragmentUtrvs.find(id => String(id) === newId)) {
        secondaryCompData.fragmentUtrvs.push(_id);
        if ('markModified' in utrv) {
          utrv.markModified('compositeData');
        }
      }

      const { code } = <CompositeUtrConfigInterface>surveyForm.config;
      secondaryCompData.configCode = code;
      secondaryCompData.surveyId = this.surveyModel._id;

      return;
    }


    if (!compData.fragmentUtrvs.find(id => id.toString() === newId)) {
      compData.fragmentUtrvs.push(_id);

      // Don't reset utrGroup fragment
      if (!surveyForm || !surveyForm.utrGroupConfig) {
        this.resetAsComposite(utrv);
      }

      if ('markModified' in utrv) {
        utrv.markModified('compositeData');
      }
    }
  }

  private resetAsComposite(utrv: UtrvProcessData) {
    utrv.verificationRequired = false;
    utrv.evidenceRequired = false;
    utrv.noteRequired = false;

    // Composite utrvs do not have escalation policy
    delete (utrv.escalationPolicy);
  }

  private createEmptyCompositeData(configCode?: string): CompositeData {
    return {
      fragmentUtrvs: [],
      surveyId: this.surveyModel._id,
      blueprint: this.surveyModel.sourceName,
      configCode,
    };
  }

  private configHasDifferentOwner(surveyForm: SurveyForm) {
    if (!surveyForm || !surveyForm.config) {
      return false;
    }

    const { ownerSourceName } = <CompositeUtrConfigInterface>surveyForm.config;
    if (typeof ownerSourceName !== 'string') {
      return false;
    }

    return ownerSourceName !== this.surveyModel.sourceName;
  }

  protected async loadRelatedUtrvValues() {

    if (activeBlueprints.includes(this.surveyModel.sourceName as Blueprints)) {
      return;
    }

    const utrCodes = extractUtrCodes(this.surveyForms);
    const data = await SurveyProcessRepository.getReferencedData(this.surveyModel, utrCodes);

    const relatedUtrv = data.map(({ universalTracker: [utr] }) => this.getUniqueUtrvCode(utr.code));

    wwgLogger.info('Found %d secondary utrvs, %j', data.length, relatedUtrv);
    const loadedCodes = new Set();
    data.forEach((utrv) => {
      const { universalTracker: [utr], universalTrackerId, _id } = utrv;
      if (!utr) {
        // This is commonly caused by deleted custom metrics
        return wwgLogger.info(
          'Failed to lookup universalTracker for id %s utrv: _id %s',
          universalTrackerId,
          _id
        );
      }

      this.universalTrackerCache.set(utr.code, utr);

      const uniqueCode = this.getUniqueUtrvCode(utr.code);
      const existingUtrv = this.utrvMap.get(uniqueCode);

      if (loadedCodes.has(uniqueCode)) {
        // Existing one is from earlier date
        if (existingUtrv?.created && existingUtrv.created < utrv.created) {
          this.utrvMap.set(uniqueCode, this.hydrateReferencedUtrvs(utrv));
        }
        return;
      }

      // Populate map
      this.utrvMap.set(uniqueCode, this.hydrateReferencedUtrvs(utrv));
      loadedCodes.add(uniqueCode);
    });
  }

  private hydrateReferencedUtrvs(utrv: UniversalTrackerValuePlain) {
    const universalTrackerValueModel = UniversalTrackerValue.hydrate(utrv);
    this.resetSecondaryCompData(universalTrackerValueModel);
    return universalTrackerValueModel;
  }

  private resetSecondaryCompData(utrv: UniversalTrackerValueModel) {
    if (!utrv.compositeData || !Array.isArray(utrv.compositeData.secondary)) {
      return false;
    }
    utrv.compositeData.secondary = utrv.compositeData.secondary.filter(c => {
      return c.blueprint !== this.surveyModel.sourceName;
    });
    utrv.markModified('compositeData');
  }

  private setConfigCode(utrv: UtrvProcessData, configCode: string) {
    const comp = getCompositeData(utrv);
    if (comp) {
      comp.configCode = configCode;
    }
  }
}
