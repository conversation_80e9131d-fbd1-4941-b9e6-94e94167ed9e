/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Survey, { SurveyListItem, SurveyModelPlain } from '../../models/survey';
import { InitiativePlain } from '../../models/initiative';
import { getCsvName } from '../assurance/csvContext';
import {
  getStandardsRepository,
  StandardsData,
  StandardsRepository,
} from '../../repository/StandardsRepository';
import { Blueprints, standardsSourceNames } from '../../survey/blueprints';
import { standards } from '@g17eco/core';
import { UserModel } from '../../models/user';
import { getBluePrintContribution } from '../survey/BlueprintContribution';
import { frameworkTags } from "../../models/common/universalTrackerTags";
import { SurveyRepository } from "../../repository/SurveyRepository";
import { UniversalTrackerValuePlain, UtrvAssuranceStatus } from "../../models/universalTrackerValue";
import { ActionList } from "../utr/constants";
import { wwgLogger } from "../wwgLogger";
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { isUtrvAssuranceComplete } from '../assurance/assuranceStatus';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { ObjectId } from 'bson';


interface StandardsRating extends StandardsData {
  rating: number;
  subheading: string;
}

interface StandardsMap {
  [key: string]: number;
}

interface StandardsFrameworksMap {
  [key: string]: {
    total: number;
    verified: number;
    sentToAssurer: number;
    assured: number;
    restated: number;
  };
}

export class StandardManager {

  constructor(
    private standardRepo: StandardsRepository,
    private initiativeRepo: typeof InitiativeRepository,
    private blueprintContribution = getBluePrintContribution(),
  ) {

  }

  public async getStandardByInitiative(initiative: InitiativePlain, user: UserModel) {

    const conditions: Record<string, any> = {
      initiativeId: initiative._id,
      deletedDate: { $exists: false },
      sourceName: { $in: standardsSourceNames },
    };

    if (!await InitiativePermissions.canAccess(user, initiative._id)) {
      conditions.completedDate = { $exists: true };
    }

    const surveyList = await Survey.find(conditions)
      .sort({ effectiveDate: -1 })
      .lean().exec() as SurveyModelPlain[];

    if (surveyList.length === 0) {
      return { list: <any>[], standards: <any>[] }
    }

    const list = surveyList.map((survey: SurveyModelPlain) => {
      return ({
        _id: survey._id,
        name: getCsvName({ initiative, survey, _id: survey._id }),
        scope: survey.scope,
        effectiveDate: survey.effectiveDate,
        completedDate: survey.completedDate,
        type: survey.type,
        period: survey.period
      }) as SurveyListItem
    });

    const surveyStandards = await this.getSurveyStandards(surveyList[0]);

    return { list, standards: surveyStandards };
  }

  public async getSurveyStandards(survey: SurveyModelPlain) {

    // find utrvs with verified status
    const utrvs = await this.standardRepo.findSurveyStandardValues(survey);

    const keys = Object.keys(standards);
    const map = keys.reduce((a, c) => {
      a[c] = 0;
      return a;
    }, <StandardsMap>{})

    const contribution = await this.blueprintContribution
      .getContributions(survey.sourceName as Blueprints);

    const relatedQuestionCount = utrvs.reduce((acc: any, { type, alternatives, code }) => {
      keys.forEach((standardCode: keyof StandardsMap) => {
        if (standardCode === 'sdg') {
          if (contribution[code]) {
            acc[standardCode] += 1;
          }
          return;
        }
        const isType = this.isType(standardCode, type, alternatives)
        acc[standardCode] += isType ? 1 : 0;
      });
      return acc;
    }, map);

    // return standards information
    const standardData = await this.standardRepo.findByCodes(Object.keys(map));
    const ratings = Object.entries(relatedQuestionCount).map(([k, v]) => {
      const standard = standardData.find(c => c.code === k);
      if (standard && Number(v) > 0) {
        return <StandardsRating>{
          ...standard,
          rating: <number>v,
          subheading: 'Questions',
        };
      }
    }).filter(Boolean) as StandardsRating[];
    const standardsWithRating = ratings.sort((a, b) => b.rating - a.rating);

    return this.fillWithPreferredStandards(standardsWithRating, standardData);
  }

  private getStandardsFrameworksEmptyMap(keys: string[]) {
    return keys.reduce((a, c) => {
      a[c] = {
        total: 0,
        verified: 0,
        sentToAssurer: 0,
        assured: 0,
        restated: 0,
      }
      return a;
    }, <StandardsFrameworksMap>{})
  }

  private updateStatusesCount({
    countsMap,
    key,
    utrv
  }: {
    countsMap: StandardsFrameworksMap;
    key: string;
    utrv: Pick<UniversalTrackerValuePlain, 'status' | 'assuranceStatus'>
  }) {
    countsMap[key].total++;
    countsMap[key].verified += utrv.status === ActionList.Verified ? 1 : 0;
    countsMap[key].sentToAssurer += utrv.assuranceStatus ? 1 : 0;
    countsMap[key].assured += isUtrvAssuranceComplete(utrv.assuranceStatus) ? 1 : 0;
    countsMap[key].restated += utrv.assuranceStatus === UtrvAssuranceStatus.Restated ? 1 : 0;
  }

  /**
   * Get count of standard and frameworks verified/total answers
   * returns: [standard] => { total:x, verified:x, sentToAssurer:x, assured:x, restated:x }
   * @param survey
   */
  public async getSurveyVerifiedByGroup(survey: SurveyModelPlain) {
    const globalCountKey = 'all';
    // find utrvs with verified status
    const utrvs = await SurveyRepository.getSurveyQuestionByMatch(survey, {
      statuses: [
        ActionList.Created,
        ActionList.Updated,
        ActionList.Verified,
        ActionList.Rejected,
        ActionList.NotReported,
      ],
    });
    if (utrvs.length === 0) {
      return [];
    }

    const customModules = await this.initiativeRepo.getOrganizationKpiGroups(survey.initiativeId);
    const utrModulesMap: Map<string, ObjectId[]> = new Map();
    customModules.forEach((metricGroup) => {
      const utrIds = metricGroup.universalTrackers;
      utrIds.forEach((utrId) => {
        utrModulesMap.set(utrId.toString(), [...(utrModulesMap.get(utrId.toString()) ?? []), metricGroup._id]);
      });
    });
    const keys = Object.keys(standards)
      .concat(frameworkTags)
      .concat(customModules.map((metricGroup) => metricGroup._id.toString()))
      .concat([globalCountKey]);
    const totalsMap = this.getStandardsFrameworksEmptyMap(keys);
    const contribution = await this.blueprintContribution.getContributions(survey.sourceName as Blueprints);

    return utrvs.reduce((acc: StandardsFrameworksMap, utrv: UniversalTrackerValuePlain) => {
      if (!utrv.universalTracker) {
        wwgLogger.warn('Missing utr on utrv', { utrv: utrv._id });
        return;
      }
      const utr = utrv.universalTracker;

      // count verified into standard or framework code
      // framework is in tags
      if (utr.tags) {
        for (const tag in utr.tags) {
          // framework code tag has some values and is valid
          if (utr.tags[tag].length > 0 && frameworkTags.includes(tag)) {
            this.updateStatusesCount({
              countsMap: acc,
              key: tag,
              utrv,
            });
          }
        }
      }

      // sdg contribution from utr code
      if (contribution[utr.code]) {
        this.updateStatusesCount({
          countsMap: acc,
          key: 'sdg',
          utrv,
        });
      }

      // standards are in type
      keys.forEach((standardCode) => {
        if (this.isType(standardCode, utr.type, utr.alternatives)) {
          this.updateStatusesCount({
            countsMap: acc,
            key: standardCode,
            utrv,
          });
        }
      });

      // custom metric modules count
      if (utrModulesMap.has(utr._id.toString())) {
        utrModulesMap.get(utr._id.toString())?.forEach((module) => {
          this.updateStatusesCount({
            countsMap: acc,
            key: module.toString(),
            utrv,
          });
        });
      }

      this.updateStatusesCount({
        countsMap: acc,
        key: globalCountKey,
        utrv,
      });
      return acc;
    }, totalsMap);
  }

  /**
   * Ensure that response contains at least preferred standards entries
   * @param standardsWithRating
   * @param standardData
   * @private
   */
  private fillWithPreferredStandards(standardsWithRating: StandardsRating[], standardData: StandardsData[]) {
    const preferredStandardsCode = this.getPreferredStandardCodes();
    if (standardsWithRating.length >= preferredStandardsCode.length) {
      return standardsWithRating;
    }

    // Add preferred standards to populate data
    // (want to display at least as many as preferred standards)
    for (const code of preferredStandardsCode) {
      if (!standardsWithRating.some(s => s.code === code)) {

        const standard = standardData.find(c => c.code === code);
        if (standard) {
          standardsWithRating.push(<StandardsRating>{
            ...standard,
            rating: 0,
            subheading: 'Questions',
          });
        }
      }

      // Check if there is enough standards now
      if (standardsWithRating.length >= preferredStandardsCode.length) {
        break;
      }
    }

    return standardsWithRating;
  }

  private getPreferredStandardCodes() {
    return [
      "gri",
      "sdg",
      "cdp_climate",
      "cdp_forest",
      "cdp_water",
      "refinitiv",
      "trucost",
      "blabs",
      "futurefit",
    ];
  }

  private isType(
    standardCode: keyof StandardsMap,
    type: string,
    alternatives?: { [p: string]: any }
  ) {
    return standardCode === type || alternatives?.[standardCode];
  }
}

export const createStandardManager = () => {
  return new StandardManager(getStandardsRepository(), InitiativeRepository, getBluePrintContribution());
};
