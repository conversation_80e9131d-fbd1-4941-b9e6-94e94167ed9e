import { type CreateEditorArgs, ParagraphNode } from 'lexical';
import { wwgLogger } from '../wwgLogger';
import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode } from '@lexical/rich-text';
import { IXBRLNode } from './lexical/nodes/IXBRLNode';
import { ReportDocumentType } from '../../models/reportDocument';
import { getDefaultESRSMappingList } from './csrd/CsrdMappingList';
import { getDefaultIFRSMappingList } from './issb/IssbMappingList';
import type { XBRLMapping } from './types';

export const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, IXBRLNode, ParagraphNode],
};

const defaultMappings = {
  [ReportDocumentType.CSRD]: getDefaultESRSMappingList(),
  [ReportDocumentType.ISSB]: getDefaultIFRSMappingList(),
};

export const getMappingsByType = ({
  type,
  overrides = {},
}: {
  type: ReportDocumentType;
  overrides?: XBRLMapping;
}): XBRLMapping => {
  return defaultMappings[type].reduce((acc, item) => {
    if (!acc[item.factName]) {
      acc[item.factName] = {
        factName: item.factName,
        utrCode: item.utrCode,
        valueListCode: item.valueListCode,
      };
    }
    return acc;
  }, overrides);
};
