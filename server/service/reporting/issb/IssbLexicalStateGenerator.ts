/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createHeadlessEditor } from '@lexical/headless';
import { $getRoot, type SerializedEditorState } from 'lexical';
import { ReportDocumentType, type ReportDocumentPlain } from '../../../models/reportDocument';
import type { LoggerInterface } from '../../wwgLogger';
import { wwgLogger } from '../../wwgLogger';
import type { SectionData } from '../xhtml/types';
import { XbrlTracker } from '../XbrlTracker';
import { buildTableOfContents } from './sections/tableOfContents';
import type { ISSBMappingItem, GenerateLexicalStateParams } from '../types';
import { editorConfig } from '../utils';
import { buildRequirementsAndFoundation } from './sections/requirementsAndFoundation';
import { buildGovernance } from './sections/governance';
import { buildStrategy } from './sections/strategy';
import { buildRiskManagement } from './sections/riskManagement';
import { buildMetricsAndTargets } from './sections/metricsAndTargets';
import { LexicalStateGenerator } from '../lexical/LexicalStateGenerator';
import { getReportGenerator } from '../xhtml/ReportGenerator';

export class IssbLexicalStateGenerator extends LexicalStateGenerator {
  constructor(private logger: LoggerInterface) {
    super(ReportDocumentType.ISSB, getReportGenerator());
  }

  public async generateTemplateLexicalState(params: GenerateLexicalStateParams<ISSBMappingItem>) {
    const { initiative, survey, mapping, utrCodeToUtrvMap } = params;

    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();
    const _sectionData: SectionData<ISSBMappingItem> = {
      initiative,
      mapping,
      utrCodeToUtrvMap,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...buildTableOfContents(),
          ...buildRequirementsAndFoundation(),
          ...buildGovernance(),
          ...buildStrategy(),
          ...buildRiskManagement(),
          ...buildMetricsAndTargets()
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  public async downloadReport({
    reportDocument,
    editorState,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
  }) {
    return super.downloadReport({ reportDocument, editorState, preview: false });
  }
}

let instance: IssbLexicalStateGenerator;
export const getIssbLexicalStateGenerator = () => {
  if (!instance) {
    instance = new IssbLexicalStateGenerator(wwgLogger);
  }
  return instance;
};
