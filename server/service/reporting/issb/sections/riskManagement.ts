import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { createListNode } from '../../lexical/utils';

export function buildRiskManagement(): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 4. Risk Management'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This section describes the processes the company uses to identify, assess, prioritize, and monitor sustainability-related risks and opportunities.'
    )
  );

  const sectionNodes = [
    {
      section: '4.1. Identification and Assessment Process',
      description: 'The process used to identify sustainability-related risks and opportunities.',
      keyDisclosures: [
        'The inputs to the process (e.g., data sources, scope of operations).',
        'How the nature, likelihood, and magnitude of risks are assessed.',
        'How climate-related scenario analysis informs the identification of risks.',
      ],
    },
    {
      section: '4.2. Management and Monitoring Process',
      description: 'The processes for managing and monitoring identified risks.',
      keyDisclosures: [
        'How risks are prioritized relative to other types of risk.',
        'How risks are monitored over time.',
        'Description of any changes to the risk management process from the previous reporting period.',
      ],
    },
    {
      section: '4.3. Integration with Overall Risk Management',
      description:
        'How the risk management process for sustainability-related issues is integrated with the company\'s overall enterprise risk management framework.',
      keyDisclosures: [
        'Explanation of how sustainability risks are incorporated into the overall risk register.',
        'The role of the board risk committee in overseeing sustainability-related risks.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    return [subsectionHeading, subsectionDescription, keyDisclosuresList];
  });

  return [sectionHeading, sectionDescription, ...sectionNodes];
}
