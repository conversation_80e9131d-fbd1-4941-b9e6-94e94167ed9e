import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { createListNode } from '../../lexical/utils';

export function buildMetricsAndTargets(): LexicalNode[] {
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode('🏢 5. Metrics and Targets'));
  const sectionDescription = $createParagraphNode();
  sectionDescription.append(
    $createTextNode(
      'This section includes the performance metrics and targets used to measure and manage material sustainability-related risks and opportunities.'
    )
  );

  const sectionNodes = [
    {
      section: '5.1. Greenhouse Gas (GHG) Emissions',
      description:
        'Disclosure of absolute GHG emissions, measured in accordance with the GHG Protocol Corporate Standard.',
      keyDisclosures: [
        'Scope 1 GHG emissions (gross, in metric tons of CO2 equivalent).',
        'Scope 2 GHG emissions (gross, location-based, in metric tons of CO2 equivalent).',
        'Scope 3 GHG emissions (disclosing the 15 categories, where material).',
      ],
    },
    {
      section: '5.2. Climate-Related Targets',
      description: 'The targets the company has set to mitigate or adapt to climate change.',
      keyDisclosures: [
        'The specific, measurable target.',
        'The base year from which progress is measured.',
        'The timeframe for achieving the target.',
        'How the latest international agreements on climate change have informed the target.',
        'Performance against the target in the current reporting period.',
      ],
    },
    {
      section: '5.3. Industry-Specific Metrics',
      description: 'Metrics that are relevant to the company\'s industry, based on the SASB Standards.',
      keyDisclosures: [
        'Disclosure of relevant metrics from the applicable SASB industry standard.',
        'Any company-developed metrics used to measure performance.',
      ],
    },
    {
      section: '5.4. Remuneration-Linked Metrics',
      description: 'Information on how sustainability-related performance is linked to executive remuneration.',
      keyDisclosures: [
        'A description of whether and how sustainability-related considerations are factored into remuneration policies.',
        'The percentage of executive remuneration that is linked to achieving sustainability targets.',
      ],
    },
  ].flatMap(({ section, description, keyDisclosures }) => {
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(section));

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));

    const keyDisclosuresList = createListNode(keyDisclosures);

    return [subsectionHeading, subsectionDescription, keyDisclosuresList];
  });

  return [sectionHeading, sectionDescription, ...sectionNodes];
}
