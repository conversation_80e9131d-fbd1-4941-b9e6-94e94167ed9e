/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { camelCaseToWords } from '../../util/string';
import type { DefinitionMapItem, CSRDMappingItem } from './types';

export interface MappedExternalItem {
  mappingItem: CSRDMappingItem;
  definition: DefinitionMapItem | undefined;
  type: 'csrd';
}

export interface ExternalMappingItem {
  /** The code of the mapping, usually the factName **/
  mappingCode: string;
  /** standard type like csrd, issb etc. **/
  type: string;
  name: string;
  utrCode: string;
  valueListCode?: string;
  references?: DefinitionMapItem['references'];
}

export type ExternalDefinitionItem = Pick<ExternalMappingItem, 'mappingCode' | 'name' | 'references' | 'type'> & {
  utrs: { utrCode: string; valueListCode?: string }[];
};

type GetNameParams = { label?: string; factName: string };
export const getMappedName = ({ label, factName }: GetNameParams) => {
  return label ?? camelCaseToWords(factName.replace('esrs:', ''));
};
export const getMappedItem = ({ type, mappingItem, definition }: MappedExternalItem) => {
  return {
    type,
    mappingCode: mappingItem.factName,
    name: getMappedName({ label: definition?.label, factName: mappingItem.factName }),
    utrCode: mappingItem.utrCode,
    valueListCode: mappingItem.valueListCode,
    references: definition?.references,
  } satisfies ExternalMappingItem;
};
