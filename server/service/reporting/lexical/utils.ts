import { $createListItemNode, $createListNode } from '@lexical/list';
import { $createParagraphNode, $createTextNode } from 'lexical';

export const createListNode = (items: string[]) => {
  const list = $createListNode('number');
  items.forEach((itemText) => {
    const listItem = $createListItemNode();
    const paragraph = $createParagraphNode();
    paragraph.append($createTextNode(itemText));
    listItem.append(paragraph);
    list.append(listItem);
  });
  return list;
};
