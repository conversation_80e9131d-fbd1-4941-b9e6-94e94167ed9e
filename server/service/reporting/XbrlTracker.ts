/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type {
  ContextId,
  DebugFact,
  Fact,
  FactId,
  FactsMap,
  FactTag,
  NonFractionFact,
  NonNumericFact,
  UnitId,
} from './xhtml/types';

type OptionalRefs = {
  id?: FactId;
  contextRef?: ContextId;
};
type NonFractionTag = Omit<NonFractionFact, 'id' | 'contextRef' | 'tag'> & OptionalRefs;
type NumericFact = Omit<NonNumericFact, 'id' | 'contextRef'> & OptionalRefs;

export class XbrlTracker {
  constructor(private period: string = '2024-01-01/2025-01-01') {}

  private facts: Record<string, (Fact & { rawValue?: string | number }) | undefined> = {};

  private units: Record<string, string> = {};

  private debugFacts: FactsMap = {};
  // Start from 1 as the example
  factCounter = 1;

  contextCounter = 1;

  unitCounter = 1;

  getFactId = (): FactId => `fact-${this.factCounter++}`;
  getContextId = (): ContextId => `c-${this.contextCounter++}`;
  getUnitId = (): UnitId => `u-${this.unitCounter++}`;

  addUnitRef(unit: string, id?: UnitId) {
    if (id === undefined) {
      id = this.getUnitId();
      this.units[id] = unit;
      return id;
    }

    this.units[id] = unit;
    return id;
  }

  public getDebugFacts = () => this.debugFacts;

  addNonNumericFact(fact: NumericFact, rawValue?: string | number) {
    // Add the fact to the facts object
    const factId = fact.id ?? this.getFactId();
    const contextId = fact.contextRef ?? this.getContextId();
    const completeFact = {
      ...fact,
      type: 'fact',
      id: factId,
      contextRef: contextId,
    } as FactTag;

    this.facts[factId] = { ...completeFact, rawValue };

    this.debugFacts[factId] = {
      v: this.getV(rawValue),
      f: fact.format,
      a: {
        c: fact.name,
        e: 'e:frag',
        p: this.period,
      },
    } satisfies DebugFact;

    return completeFact;
  }

  private getV = (rawValue: string | number | undefined) => {
    return rawValue !== undefined ? String(rawValue) : '';
  };

  addFactNonFraction(fact: NonFractionTag, rawValue?: string | number) {
    // Add the fact to the facts object
    const factId = this.getFactId();
    const completeFact = {
      ...fact,
      type: 'fact',
      tag: 'ix:nonFraction',
      id: factId,
      contextRef: this.getContextId(),
    } as FactTag;

    this.facts[factId] = { ...completeFact, rawValue };

    this.debugFacts[factId] = {
      v: this.getV(rawValue),
      a: {
        c: fact.name,
        e: 'e:frag',
        p: this.period,
        u: fact.unitRef ? this.units[fact.unitRef] : undefined,
      },
      f: fact.format,
      d: fact.decimals,
    } satisfies DebugFact;

    return completeFact;
  }

  addFactNonFractionInline({ name, value }: { name: string; value: string | number }) {
    return this.addFactNonFraction(
      {
        name,
        children: [
          {
            type: 'inline',
            content: value,
          },
        ],
      },
      value
    );
  }
}
