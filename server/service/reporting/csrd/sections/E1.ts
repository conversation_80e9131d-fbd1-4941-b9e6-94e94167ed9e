import { $createTextNode } from 'lexical';
import { $createHeadingNode } from '@lexical/rich-text';
import type { LexicalNode } from 'lexical/LexicalNode';
import type { SectionData } from '../../xhtml/types';
import { getContextualIxbrlNodes, MAJOR_SECTION } from '../utils';
import type { CSRDMappingItem } from '../../types';

export function buildE1Section(sectionData: SectionData<CSRDMappingItem>): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('🌿 ESRS E1 – Climate Change'));

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E1.code,
  });

  return [heading, ...contextualIxbrlNodes];
}
