import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { getStringData, MAJOR_SECTION, getContextualIxbrlNodes } from '../utils';
import type { LexicalNode } from 'lexical/LexicalNode';
import { $createIxbrlNode } from '../../lexical/nodes/IXBRLNode';
import type { SectionData } from '../../xhtml/types';
import type { CSRDMappingItem } from '../../types';

export function buildE3Section(sectionData: SectionData<CSRDMappingItem>): LexicalNode[] {
  const { mapping, utrCodeToUtrvMap, tracker } = sectionData;
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode('[E3-4] Water consumption'));

  const ixbrlNode = $createIxbrlNode({
    tag: 'ix:nonFraction',
    name: 'esrs:WaterConsumption',
    format: 'ixt4:num-dot-decimal',
    decimals: 4,
    unitRef: tracker.addUnitRef('m3', 'u-100'),
    contextRef: tracker.getContextId(),
    factId: tracker.getFactId(),
  });

  ixbrlNode.append(
    $createTextNode(
      getStringData({
        factName: 'esrs:WaterConsumption',
        utrCodeToUtrvMap,
        mapping,
      })
    )
  );

  const para = $createParagraphNode();
  para.append(
    $createTextNode(
      'Water stewardship is at the heart of our operations and central to our commitment to sustainable resource management. ' +
        'We recognise our responsibility and potential to make a significant positive impact on global water resources. ' +
        'In the reporting period, our total water consumption '
    ),
    ixbrlNode,
    $createTextNode(' m3 was drawn from various sources including municipal supplies, groundwater, and surface water.')
  );

  const para2 = $createParagraphNode();
  const waterIntensity = $createIxbrlNode({
    tag: 'ix:nonFraction',
    name: 'esrs:WaterIntensityTotalWaterConsumptionPerNetRevenue',
    format: 'ixt4:num-dot-decimal',
    decimals: 4,
    unitRef: tracker.addUnitRef('u-106', 'u-100'),
    contextRef: tracker.getContextId(),
    factId: tracker.getFactId(),
  });

  waterIntensity.append(
    $createTextNode(
      getStringData({
        factName: 'esrs:WaterIntensityTotalWaterConsumptionPerNetRevenue',
        utrCodeToUtrvMap,
        mapping,
      })
    )
  );

  para2.append($createTextNode('Water intensity ratio (total water consumption per net revenue): '), waterIntensity);

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E3.code,
  });

  return [heading, para, para2, ...contextualIxbrlNodes];
}
