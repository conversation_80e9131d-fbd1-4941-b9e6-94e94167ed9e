/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createHeadlessEditor } from '@lexical/headless';
import { $getRoot, type SerializedEditorState } from 'lexical';
import { ReportDocumentType, type ReportDocumentPlain } from '../../../models/reportDocument';
import type { LoggerInterface } from '../../wwgLogger';
import { wwgLogger } from '../../wwgLogger';
import type { SectionData } from '../xhtml/types';
import { XbrlTracker } from '../XbrlTracker';
import { buildIntro } from './sections/intro';
import { buildE1Section } from './sections/E1';
import { buildE2Section } from './sections/E2';
import { buildE3Section } from './sections/E3';
import { buildE4Section } from './sections/E4';
import { buildE5Section } from './sections/E5';
import { buildS1Section } from './sections/S1';
import { buildS2Section } from './sections/S2';
import { buildS3Section } from './sections/S3';
import { buildS4Section } from './sections/S4';
import { buildG1Section } from './sections/G1';
import { buildGeneralInformation } from './sections/general';
import { buildEnvironmentalInformation } from './sections/environmental/environmental';
import { buildTableOfContents } from './sections/tableOfContents';
import type {
  CSRDMappingItem,
  GenerateLexicalStateParams,
} from '../types';
import { editorConfig } from '../utils';
import { LexicalStateGenerator } from '../lexical/LexicalStateGenerator';
import { getReportGenerator } from '../xhtml/ReportGenerator';

export class CsrdLexicalStateGenerator extends LexicalStateGenerator {
  constructor(private logger: LoggerInterface) {
    super(ReportDocumentType.CSRD, getReportGenerator());
  }

  public async generateTemplateLexicalState(params: GenerateLexicalStateParams<CSRDMappingItem>) {
    const { initiative, survey, mapping, utrCodeToUtrvMap } = params;

    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();
    const sectionData: SectionData<CSRDMappingItem> = {
      initiative,
      mapping,
      utrCodeToUtrvMap,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...buildTableOfContents(sectionData),
          ...buildGeneralInformation(sectionData),
          ...buildIntro(sectionData),
          ...buildEnvironmentalInformation(sectionData),
          ...buildE1Section(sectionData),
          ...buildE2Section(sectionData),
          ...buildE3Section(sectionData),
          ...buildE4Section(sectionData),
          ...buildE5Section(sectionData),
          ...buildS1Section(sectionData),
          ...buildS2Section(sectionData),
          ...buildS3Section(sectionData),
          ...buildS4Section(sectionData),
          ...buildG1Section(sectionData)
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  public async downloadReport({
    reportDocument,
    editorState,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
  }) {
    return super.downloadReport({ reportDocument, editorState, preview: true });
  }
}

let instance: CsrdLexicalStateGenerator;
export const getCsrdLexicalStateGenerator = () => {
  if (!instance) {
    instance = new CsrdLexicalStateGenerator(wwgLogger);
  }
  return instance;
};
