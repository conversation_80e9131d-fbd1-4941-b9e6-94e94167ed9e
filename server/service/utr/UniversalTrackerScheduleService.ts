/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import UniversalTrackerSchedule, { UniversalTrackerSchedulePlain } from '../../models/universalTrackerSchedule';
import * as CronParser from 'cron-parser';
import UniversalTrackerManager, { CreateUniversalTrackerValueData } from './UniversalTrackerValueManager';
import moment = require('moment');
import { ObjectId } from 'bson';
import { UniversalTrackerScheduleRepository } from '../../repository/UniversalTrackerScheduleRepository';
import { SourceTypes } from '../../models/public/universalTrackerValueType';

export default class UniversalTrackerScheduleService {

  static getPendingSchedules() {
    return UniversalTrackerScheduleRepository.getPendingScheduleActions();
  }

  async resetNextRunDate(resetDate: Date, scheduleId?: string) {

    const conditions: any = {
      enabled: true,
      endDate: {
       $gte: new Date()
      }
    };

    if (scheduleId) {
      conditions._id = new ObjectId(scheduleId);
    }

    const data = await UniversalTrackerSchedule.find(conditions).exec();

    const endOfToday = moment(resetDate).endOf('day').toDate();
    console.log(`Found ${data.length} schedules, resetting to ` + endOfToday.toISOString());

    const result = { count: data.length, success: false, failCount: 0, successCount: 0 };

    for (const model of data) {
      model.nextRunDate = endOfToday;

      try {
        await model.save();
        result.successCount++;
      } catch (e) {
        console.log(e);
        result.failCount++;
      }
    }

    return { ...result, success: true };
  }

  async process(data: UniversalTrackerSchedulePlain[]) {

    const result = {count: 0, success: false, failCount: 0, successCount: 0};

    for (const schedule of data) {
      // Check if data was missed
      try {

        // Generate utr values
        const nextTrackerDates = this.generateNextTrackerDates(schedule);
        await this.addTrackers(nextTrackerDates, schedule);
        result.successCount += nextTrackerDates.length;

        const nextRunDate = this.createNextRunDate(schedule, nextTrackerDates.pop());
        const now = new Date();
        const endDateIsInTheFuture = now < schedule.endDate;

        await UniversalTrackerSchedule.findByIdAndUpdate(
          schedule._id,
          {
            nextRunDate: nextRunDate,
            lastRunDate: now,
            history: schedule.history,
            active: endDateIsInTheFuture
          }
        );

        result.count++;
      } catch (e) {
        console.log(e);
        result.failCount++;
      }
    }

    result.success = true;
    return result;
  }

  private async addTrackers(dates: Date[], schedule: UniversalTrackerSchedulePlain) {
    if (dates.length > 0) {
      for (const date of dates) {
        const tracker = await this.scheduleTrackerForDate(schedule, date);
        schedule.history.push({
          universalTrackerValueId: tracker._id,
          effectiveDate: tracker.effectiveDate,
        });
      }
    }
    return dates;
  }

  private createDates(schedule: UniversalTrackerSchedulePlain, options: { currentDate: Date; endDate: Date; iterator: boolean }) {
    const cronDates = [];
    const interval = CronParser.parseExpression(schedule.cronSchedule, options);
    while (interval.hasNext()) {
      const cronDate: any = interval.next();
      cronDates.push(cronDate.value.toDate());
    }

    return cronDates;
  }

  private async scheduleTrackerForDate(schedule: UniversalTrackerSchedulePlain, date: Date) {
    const tracker: CreateUniversalTrackerValueData = {
      sourceType: SourceTypes.Manual,
      stakeholders : schedule.stakeholders,
      universalTrackerId: schedule.universalTrackerId,
      initiativeId: schedule.initiativeId,
      type: schedule.type,
      verificationRequired: schedule.verificationRequired,
      evidenceRequired: schedule.evidenceRequired,
      effectiveDate: date,
    };

    return UniversalTrackerManager.create(tracker);
  }

  private generateNextTrackerDates(schedule: UniversalTrackerSchedulePlain): Date[] {
    // If we don't have nextRunDate, assume this is the first run and use startDate
    const currentDate = schedule.nextRunDate || schedule.startDate;

    // Ether end of today or endDate, earlier win
    const endOfToday = moment().endOf('day').toDate();
    const endDate = endOfToday < schedule.endDate ? endOfToday : schedule.endDate;

    return this.createDates(schedule, {
      currentDate: moment(currentDate).startOf('day').toDate(),
      endDate: endDate,
      iterator: true
    });
  }

  private createNextRunDate(schedule: UniversalTrackerSchedulePlain, lastScheduledDate?: Date) {

    if (!lastScheduledDate) {
      return schedule.nextRunDate;
    }

    const clonedDate = new Date(lastScheduledDate.getTime());
    clonedDate.setHours(23, 59, 59);

    const interval = CronParser.parseExpression(schedule.cronSchedule, {
      currentDate: clonedDate,
      endDate: schedule.endDate,
      iterator: true
    });

    if (interval.hasNext()) {
      const cronDate: any = interval.next();
      const date = cronDate.value.toDate();
      date.setHours(0, 0, 0);
      return date;
    }

    return schedule.endDate;
  }
}
