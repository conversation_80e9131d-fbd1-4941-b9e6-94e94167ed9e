/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */



import { expect } from "chai";
import { gri2020 } from '../../../server/survey/gri/gri2020';
import {
  Blueprint,
  getBlueprintRepository, SurveyForm,
} from '../../../server/repository/BlueprintRepository';
import { CompositeUtrConfigInterface } from '../../../server/survey/compositeUtrConfigs';
import { BlueprintValidation } from '../../../server/service/survey/BlueprintValidation';

describe('gri2020 validation', function () {

  const validator = new BlueprintValidation(
    getBlueprintRepository()
  );

  describe('ensure all codes are in lower case', function () {

    it('should extract all unique value chain codes', async () => {
      const errors = validator.validateUtrCodes(gri2020);
      expect(errors, errors[0]?.title).to.be.empty;
    });
  });

  describe('Expanded blueprint', () => {
    const blueprintRepo = getBlueprintRepository();
    let blueprint: Blueprint;
    before(async () => {
      blueprint = await blueprintRepo.mustFindExpandedByCode(gri2020.code)
    });

    it('Validate fragments import configuration ', () => {

      const surveyForms: SurveyForm[] = [
        ...blueprint.forms,
        ...(blueprint.additionalConfigs || [])
      ];

      for (const form of surveyForms) {
        if (!form.compositeConfig) {
          continue;
        }

        const msg = `Failed to validate ${form.compositeConfig}`
        const { importConfigurationData, fragmentUtrCodes, fragmentUtrConfiguration = {} } = <CompositeUtrConfigInterface>form.config;
        if (fragmentUtrCodes) {
          Object
            .entries(importConfigurationData.variables)
            .forEach(([_, { code }]) => {
              expect(fragmentUtrCodes, msg).contain(code);
            });
        }

        const vcObjectMsg = `${msg} missing fragmentUtrConfiguration`;
        expect(fragmentUtrConfiguration, vcObjectMsg).to.be.an('object');

        // We no longer require VC configuration
        fragmentUtrCodes?.forEach((code) => {
          const message = `${msg} ${code} missing vc configuration`;
          expect(fragmentUtrConfiguration[code], message).to.be.an('array');
        })
      }
    });
  });
});
