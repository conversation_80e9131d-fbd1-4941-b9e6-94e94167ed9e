/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { blueprintTest } from './utr/blueprint/blueprintTestFixtures';
import { createCombinedUtrv } from '../factories/universalTrackerValue';
import { getCompositeUtrConfigRepository } from '../../server/repository/CompositeUtrConfigRepository';
import { allTestSurveys, testUtrConfigs } from './survey/surveySourceFixtures';
import { BlueprintRepository } from '../../server/repository/BlueprintRepository';
import { SurveyProcess } from '../../server/service/survey/SurveyProcess';
import { userOne } from './userFixtures';
import Survey from '../../server/models/survey';
import { ActionList } from '../../server/service/utr/constants';
import { BlueprintContribution } from '../../server/service/survey/BlueprintContribution';


export const getContributionWithCode = (blueprintCode: string, mockSurveyRepo: BlueprintRepository) => {
  const blueprintContribution = new BlueprintContribution(mockSurveyRepo);

  // Force custom blueprint test blueprint
  const map = (blueprintContribution as any).map;
  map[blueprintCode] = {};
  return blueprintContribution;
};

export const createMockSetup = async () => {
  const compositeUtrConfigRepository = getCompositeUtrConfigRepository(testUtrConfigs);
  const mockSurveyRepo = new BlueprintRepository(allTestSurveys, compositeUtrConfigRepository);

  const blueprintCode = blueprintTest.code;
  const blueprint = await mockSurveyRepo.mustFindExpandedByCode(blueprintCode);
  const blueprintContribution = getContributionWithCode(blueprintCode, mockSurveyRepo);

  return new SurveyProcess(
    blueprint,
    userOne,
    new Survey({ sourceName: blueprintCode }),
    blueprintContribution
  );
}

export type Override = Record<string, { utrv: any, utr?: any }>;

export const getMockUtrvData = async (process: SurveyProcess, overrideMap: Override) => {
  const utrvMap = await process.generateSurvey();
  const data: any[] = [];
  for (const [utrCode, v] of utrvMap) {
    const utrv = ('save' in v) ? v.toObject() : v;

    const o = overrideMap[utrCode] ?? { utrv: {}, utr: {} };
    data.push(createCombinedUtrv(
      utrCode,
      { ...utrv,  ...o.utrv },
      o.utr
    ));
  }
  return data;
};

export const getOverrideCode = (code: string) => {

  if (code === 'top-score') {
    return code;
  }

  const prefix = code.split('/').length > 2 ? 'fragment/top-score' : 'top-score';
  return `${prefix}/${code}`
};

type OverrideRow = [string, number | undefined, ActionList?, Date?];

export const generateOverride = (rows: OverrideRow[], defaultStatus = ActionList.Verified) => {

  return rows.reduce((a, c) => {
    const [code, value, status = defaultStatus, deletedDate] = c;
    a[getOverrideCode(code)] = { utrv: { value, status, deletedDate } }
    return a;
  }, <Override>{})
}
