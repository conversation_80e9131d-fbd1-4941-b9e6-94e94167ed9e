/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { UniversalTrackerSchedulePlain } from '../../server/models/universalTrackerSchedule';
import { userIdTwo, userIdOne } from './userFixtures';
import moment = require('moment');

export const utrScheduleOne: UniversalTrackerSchedulePlain = {
  '_id': new ObjectId('5bf566bd474f43459a7b2905'),
  'evidenceRequired': false,
  verificationRequired: false,
  'enabled': true,
  'stakeholders': {
    'stakeholder': [
      userIdOne,
      userIdTwo,
    ],
    'verifier': [
      userIdOne,
      userIdTwo
    ],
    'escalation': [
      userIdTwo
    ]
  },
  'startDate': new Date('2018-11-18T14:07:32.135Z'),
  'nextRunDate':  new Date('2018-11-21T14:07:32.135Z'),
  'lastRunDate':  new Date('2018-11-19T14:07:32.135Z'),
  'cronSchedule': '0 7 * * *',
  'name': 'My first schedule test',
  'code': 'my-first/schedule/test',
  'type': 'actual',
  'endDate': moment().add('3', 'month').toDate(),
  'universalTrackerId':  new ObjectId('5b62e8f30a25440fb6bf0164'),
  'initiativeId':  new ObjectId('5bcdf4573bbaeb63bf5a59aa'),
  'history': [],
  'created':  new Date('2018-11-11T14:07:57.799Z'),
  'userId': userIdTwo,
};

export const utrScheduleReal: UniversalTrackerSchedulePlain = {
  '_id': new ObjectId('5c12334827f40112b055e961'),
  'evidenceRequired': false,
  'enabled': true,
  'name': 'Kes - Hot Drinks',
  'code': 'wwg/kk/kpi/hot-drinks',
  'type': 'actual',
  // 'instructions': 'Please fill in your data for this week',
  'startDate': new Date('2018-12-13T00:00:00.000Z'),
  'endDate': moment().add('3', 'month').toDate(),
  'cronSchedule': '0 0 * * 5',
  'stakeholders': {
    'stakeholder': [
      new ObjectId('5b969fa3b4b6565b279f6f4d')
    ],
    'verifier': [
      new ObjectId('5b969af6b4b6565b279f6f4c'),
      new ObjectId('5b96a14351cf7b01576deff2'),
      new ObjectId('5bab60b6fae82217611ea277'),
      new ObjectId('5c1236646056681277dd1513'),
    ],
    'escalation': []
  },
  'initiativeId': new ObjectId('5c1233126056681277dd14ed'),
  'universalTrackerId': new ObjectId('5c12332327f40112b055e95f'),
  'userId': new ObjectId('5b969af6b4b6565b279f6f4c'),
  'created': new Date('2018-12-13T10:24:08.042Z'),
  'history': [
    {
      'universalTrackerValueId': new ObjectId('5c133b282d7965542f072167'),
      'effectiveDate': new Date('2018-12-14T00:00:00.000Z'),
      'created': new Date('2018-12-14T05:10:00.180Z')
    }
  ],
  'lastRunDate': new Date('2019-01-10T05:10:00.146Z'),
  'nextRunDate': new Date('2019-06-01T00:00:00.000Z'),
};

export const neverEndingSchedule: UniversalTrackerSchedulePlain = {
  "_id": new ObjectId("5d1240eb4c9dee25a8dcfcba"),
  "evidenceRequired": false,
  "verificationRequired": true,
  "enabled": true,
  "name": "Monthly WWG legal issues",
  "code": "risk/monthly-li",
  "type": "actual",
  "instructions": "Please fill in your data for this month",
  "startDate": new Date("2019-06-25T00:00:00.000Z"),
  "endDate": new Date("2019-07-01T00:00:00.000Z"),
  "cronSchedule": "0 0 * * *",
  "stakeholders": {
    "stakeholder": [
    new ObjectId("5d1201301b501125a393a1f5"),
      new ObjectId("5b96a14351cf7b01576deff2")
    ],
    "verifier": [
    new ObjectId("5d1201301b501125a393a1f5"),
      new ObjectId("5b96a14351cf7b01576deff2")
    ],
    "escalation": []
  },
  "initiativeId": new ObjectId("5c1a0f9c24bf6a2394407a0d"),
  "universalTrackerId": new ObjectId("5d12310ad81f1c25d510bc1d"),
  "userId": new ObjectId("5d1201301b501125a393a1f5"),
  "created": new Date("2019-06-25T15:42:35.175Z"),
  "history": [],
  "lastRunDate": new Date("2019-08-21T05:10:06.021Z"),
  "nextRunDate": new Date("2019-07-01T00:00:00.000Z"),
};
