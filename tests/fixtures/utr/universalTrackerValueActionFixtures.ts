/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { userOne, userTwo } from '../userFixtures';
import { initiativeOneSimpleId } from '../initiativeFixtures';
import { universalTrackerOne } from '../universalTrackerFixtures';
import { UtrvAction } from '../../../server/models/universalTrackerValue';
import { ActionList } from '../../../server/service/utr/constants';
import moment = require('moment');
import { ObjectId } from 'bson';
import { UtrValueType } from '../../../server/models/public/universalTrackerType';

export const utrValueActionOne = new ObjectId('5bfd4663761f3475d768b5c9');
export const utrValueActionTwo = new ObjectId('5bfeb8950d5d142626adee1c');

export const utrActionOne: UtrvAction = {
  _id: utrValueActionOne,
  status: ActionList.Created,
  universalTrackerId: universalTrackerOne._id,
  initiativeId: initiativeOneSimpleId,
  lastUpdated: new Date(),
  instructions: '',
  effectiveDate: moment().add(1, 'day').endOf('day').toDate(),
  universalTracker: {
    valueType: UtrValueType.Number,
    name: universalTrackerOne.name,
    type: universalTrackerOne.type,
  },
  stakeholders: {
    stakeholder: [
      userOne._id,
    ],
    verifier: [
      userTwo._id
    ],
    escalation: [
      userTwo._id
    ]
  },
  users: [
    userOne,
    userTwo,
  ]
};

export const utrActionTwo: UtrvAction = {
  _id: utrValueActionTwo,
  status: ActionList.Created,
  universalTrackerId: universalTrackerOne._id,
  initiativeId: initiativeOneSimpleId,
  lastUpdated: new Date(),
  instructions: '',
  effectiveDate: moment().add(2, 'day').endOf('day').toDate(),
  universalTracker: {
    name: universalTrackerOne.name,
    type: universalTrackerOne.type,
    valueType: universalTrackerOne.valueType,
  },
  stakeholders: {
    stakeholder: [
      userOne._id,
      userTwo._id
    ],
    verifier: [
      userTwo._id
    ],
    escalation: [
      userTwo._id
    ]
  },
  users: [
    userOne,
    userTwo,
  ]
};
