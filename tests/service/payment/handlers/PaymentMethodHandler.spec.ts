/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { PaymentMethodHandler } from "../../../../server/service/payment/handlers/PaymentMethodHandler";
import { testLogger } from "../../../factories/logger";
import { getCustomerManager } from "../../../../server/service/payment/CustomerManager";
import { expect } from "chai";
import { HandlerEvent } from "../../../../server/service/payment/handlers/HandlerType";
import { createSandbox } from "sinon";
import { initiativeStarter } from "../../../fixtures/initiativeFixtures";
import Initiative, { InitiativeWithCustomer } from "../../../../server/models/initiative";
import { createCustomer, createPaymentMethod } from "../../../fixtures/subscriptions";
import { createObjectResponse } from "../../../mocks/StripeTestClient";
import Stripe from "stripe";
import { HydratedDocument } from "mongoose";

describe('PaymentMethodHandler', () => {

  const cm = getCustomerManager();
  const handler = new PaymentMethodHandler(testLogger, cm);

  const paymentMethod = createPaymentMethod({
    customer: initiativeStarter.customer.id,
  });

  const createEvent = (event: Partial<HandlerEvent>, payment = paymentMethod) => {
    return {
      data: {
        object: payment,
      },
      type: 'payment_method.attached',
      ...event,
    } as HandlerEvent<Stripe.PaymentMethod>
  }
  const sandbox = createSandbox();
  const customerId = initiativeStarter.customer.id;

  describe('shouldHandle', () => {
    it('should handle payment method attached event ', () => {
      const event = createEvent({ type: 'payment_method.attached' });
      expect(handler.shouldHandle(event)).eq(true)
    });

    it('should not handle customer update ', () => {
      const event = createEvent({ type: 'customer.updated' });
      expect(handler.shouldHandle(event)).eq(false)
    });
  });


  describe('handle', () => {

    let initiativeWith = new Initiative(initiativeStarter) as InitiativeWithCustomer;

    beforeEach(() => {
      initiativeWith = new Initiative(initiativeStarter) as InitiativeWithCustomer;
      sandbox.stub(cm, 'findInitiativeByCustomerId').resolves(initiativeWith)
      sandbox.stub(cm, 'addDefaultInvoicePaymentMethod').resolves();
      sandbox.stub(initiativeWith, 'save').resolves(initiativeWith)
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('should skip on delete customer', async () => {

      sandbox.stub(cm, 'getRemoteCustomer').resolves(createObjectResponse({
        id: customerId,
        object: 'customer',
        deleted: true,
      }))

      const event = createEvent({ type: 'customer.updated' });
      const result = await handler.handle(event)

      expect(result.success).eq(true);
      expect(result.name).eq('PaymentMethod');
    });


    it('should assign default method', async () => {

      const customer = createCustomer({
        id: customerId,
        invoice_settings: {
          default_payment_method: paymentMethod.id
        }
      });

      sandbox.stub(cm, 'getRemoteCustomer').resolves(createObjectResponse(customer))


      const event = createEvent({ type: 'payment_method.attached' });
      const result = await handler.handle(event)

      expect(result.success).eq(true);
      expect(initiativeWith.customer.defaultPaymentMethod).eq(paymentMethod.id);
    });

    it('should assign default method when it does not yet exists on customer', async () => {
      sandbox.stub(cm, 'getRemoteCustomer').resolves(createObjectResponse(createCustomer({})))

      const event = createEvent({
        type: 'payment_method.attached',
        data: {
          object: paymentMethod,
        }
      });
      const result = await handler.handle(event)

      expect(result.success).eq(true);
      expect(initiativeWith.customer.defaultPaymentMethod).eq(paymentMethod.id);
    });
  });
});
