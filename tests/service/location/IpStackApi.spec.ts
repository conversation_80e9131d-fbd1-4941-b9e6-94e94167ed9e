/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox, match, SinonStub } from 'sinon';
import axios from 'axios';
import { getInfo } from '../../../server/service/location/IpStackApi';
import { wwgLogger } from '../../../server/service/wwgLogger';
import ContextError from '../../../server/error/ContextError';

describe('IpStack API', () => {
  let axiosGetStub: SinonStub;
  let loggerErrorStub: SinonStub;
  const sandbox = createSandbox();

  beforeEach(() => {
    axiosGetStub = sandbox.stub(axios, 'get');
    loggerErrorStub = sandbox.stub(wwgLogger, 'error');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should return extended IP location on successful response', async () => {
    const ip = '***************';
    const response = {
      data: {
        ip,
        latitude: 51.509865,
        longitude: -0.118092,
        city: 'London',
        region_name: 'England',
        country_code: 'GB',
        zip: 'SE1',
        continent_code: 'EU',
        success: true
      }
    };
    axiosGetStub.resolves(response);

    const result = await getInfo(ip);

    expect(result).to.eql({
      ip,
      latitude: 51.509865,
      longitude: -0.118092,
      city: 'London',
      state: 'England',
      country: 'GB',
      postalCode: 'SE1',
      continentCode: 'EU'
    });
  });

  it('should return IP location with only IP on error response', async () => {
    const ip = '***************';
    const response = {
      data: {
        success: false,
        error: {
          code: 104,
          type: 'missing_access_key',
          info: 'Your API key is missing.'
        }
      }
    };
    axiosGetStub.resolves(response);

    const result = await getInfo(ip);

    expect(result).to.eql({ ip });
    expect(loggerErrorStub.calledOnce).to.be.true;
    const error = loggerErrorStub.getCall(0).args[0];
    expect(error).to.be.instanceOf(ContextError);
    expect(error.message).to.equal('Failed to get IP Stack response');
    expect(error.context).to.eql({ error: response.data.error });
  });

  it('should return IP location with only IP on request failure', async () => {
    const ip = '***************';
    const error = new Error('Network Error');
    axiosGetStub.rejects(error);

    const result = await getInfo(ip);

    expect(result).to.eql({ ip });
    expect(loggerErrorStub.calledOnce).to.be.true;
    expect(loggerErrorStub.calledWith(error)).to.be.true;
  });
});
