/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { expect } from 'chai';
import {
  ConvertDataParams,
  CustomReportData,
  getCustomReportGenerator
} from '../../../server/service/custom-report/CustomReportGenerator';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import { createUtr } from '../../fixtures/universalTrackerFixtures';
import { ObjectId } from 'bson';
import { ColumnType, TableColumn, UtrValueType } from '../../../server/models/public/universalTrackerType';
import { allValueLists, valueListTestTable } from '../../fixtures/valueListFixtures';
import { ActionList, DataPeriods, UtrvType } from '../../../server/service/utr/constants';
import { getGroup } from '@g17eco/core';
import { utrTableOne, utrTableOneUtrv, utrTableOneWithOneGroupBy, utrTableOneWithTwoGroupBy, utrTableTwo } from '../../fixtures/utr/utrTableFixtures';
import { Option } from '../../../server/models/public/valueList';
import { ReportUtrv } from '../../../server/service/custom-report/reportTypes';
import { NotApplicableTypes } from '../../../server/models/universalTrackerValue';
import { NumberScale, SupportedMeasureUnits } from '../../../server/service/units/unitTypes';
import { getStandardName } from '../../../server/service/utr/standards';
import { generateMapping } from '../../../server/service/custom-report/dataResolverUtil';
import { surveyOne } from '../../fixtures/survey';
import { SurveyType } from '../../../server/models/survey';
import { getInitiativeReportHeader } from '../../../server/service/custom-report/utils';
import { QUESTION } from '../../../server/util/terminology';

describe('CustomReportGenerator', function () {

  const cr = getCustomReportGenerator();


  const resolveListValues = (value: string[] | string, options: Option[]) => {
    const valueArray = Array.isArray(value) ? value : [value]
    return valueArray.reduce((a, code) => {
      const option = options.find(o => o.code === code);
      return option ? [...a, option.name] : a;
    }, <string[]>[]).join(', ');
  };

  const surveyId = new ObjectId();
  const createReportUtrv = (v: Partial<ReportUtrv>): ReportUtrv => ({
    _id: new ObjectId(),
    status: ActionList.Verified,
    value: undefined,
    initiativeId: initiativeOneSimple._id,
    surveyId,
    history: [],
    effectiveDate: new Date(),
    lastUpdated: new Date(),
    surveyPeriod: DataPeriods.Yearly,
    surveyType: SurveyType.Default,
    type: UtrvType.Actual,
    universalTrackerId: new ObjectId(),
    ...v,
  });

  const isListColumn = (c: TableColumn) => {
    if (!c.listId) {
      return false;
    }

    return [
      ColumnType.ValueListMulti,
      ColumnType.ValueList,
      ColumnType.Text,
    ].includes(c.type as ColumnType);
  };

  describe('convertData fn', function () {
    const utrTagMap = new Map<string, string[]>();
    const columns = cr.getSurveyColumnSetup().concat(cr.getTagColumn(utrTagMap));
    // It should start here
    const initiativeFirstIndex = columns.length

    const secondInitiativeId = new ObjectId();
    const thirdInitiativeId = new ObjectId();

    const valueLists = allValueLists;
    const period = DataPeriods.Yearly;
    const effectiveDate = new Date();
    const type = SurveyType.Default;

    const surveyFilterOne = { type, period, effectiveDate };
    const surveyFilterTwo = { type: SurveyType.Aggregation, period: DataPeriods.Monthly, effectiveDate };

    const initiatives = [
      { ...initiativeOneSimple, matchedSurveyFilters: [surveyFilterOne] },
      { _id: secondInitiativeId, name: 'second', matchedSurveyFilters: [surveyFilterOne] },
      { _id: thirdInitiativeId, name: 'third', matchedSurveyFilters: [surveyFilterOne] },
    ]

    const createConvertData = (data: CustomReportData[] = [], overrides: Partial<ConvertDataParams> = {}) => ({
      data,
      initiatives: initiatives,
      valueLists: valueLists,
      surveyFilters: [{ type, period, effectiveDate }],
      unitConfig: undefined,
      utrTagMap,
      ...overrides,
    });

    it('should generate array of data', async function () {
      const { headers, records } = await cr.convertData(createConvertData());
      expect(records).to.be.lengthOf(0);

      columns.map((col, index) => {
        expect(headers[index]).to.be.eq(col.name);
      });

      initiatives.forEach(({ name }, index) => {
        expect(headers[initiativeFirstIndex + index]).eq(name)
      })
    });


    it('should generate with single numeric question', async function () {
      const value = 12.355;
      const rowData: CustomReportData = {
        ...createUtr(new ObjectId(), 'aa', {
          valueType: UtrValueType.Number,
          type: 'gri',
        }),
        utrvs: [
          createReportUtrv({ value }),
        ],
        unitConfig: undefined,
        alternatives: {
          'cdp_water': { name: 'cdp 1', valueLabel: 'cdp label'},
          'blabs': { name: 'b labs', valueLabel: ' blabs label'},
          'futurefit': { name: 'future fit', valueLabel: ' random futurefit'},
        },
      };
      const { columns, records: [firstRow] } = await cr.convertData(createConvertData([rowData]));

      expect(firstRow[0]).to.be.eq(getStandardName(rowData.type));
      expect(firstRow[2]).to.be.eq(generateMapping(rowData));

      const typeIndex = columns.findIndex(h => h.id === 'standard');
      expect(firstRow[typeIndex]).eq(getGroup('standards', 'gri')?.shortName)

      const firstInitiativeCol = firstRow[initiativeFirstIndex];
      expect(firstInitiativeCol).eq(value)
    });

    it('should generate with numeric currency question', async function () {
      const value = 12.355;
      const rowData: CustomReportData = {
        ...createUtr(new ObjectId(), 'aa', {
          valueType: UtrValueType.Number,
          type: 'gri',
          unit: 'USD',
          unitType: SupportedMeasureUnits.currency,
          numberScale: 'millions',
        }),
        utrvs: [
          createReportUtrv({ value }),
        ],
        unitConfig: undefined,
      };
      const { columns, records: [firstRow] } = await cr.convertData(createConvertData([rowData]));

      // Unit column is blank for currency
      const unitIndex = columns.findIndex(c => c.id === 'unit');
      expect(firstRow[unitIndex]).eq('');

      const numberScaleIndex = columns.findIndex(c => c.id === 'numberScale');
      expect(firstRow[numberScaleIndex]).eq(rowData.numberScale);

      const recordValue = firstRow[initiativeFirstIndex];
      // "USD 20"
      expect(recordValue).eq(`${rowData.unit} ${value}`)

    });

    it('should handle "created" numeric currency and no value', async function () {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...createUtr(new ObjectId(), 'aa', {
          valueType: UtrValueType.Number,
          unit: 'USD',
          unitType: SupportedMeasureUnits.currency,
          numberScale: NumberScale.Hundreds,
        }),
        utrvs: [createReportUtrv({ status: 'created' })],
      };
      const { records: [firstRow] } = await cr.convertData(createConvertData([rowData]));
      // For some reason we include created etc., should not display undefined
      expect(firstRow[initiativeFirstIndex]).eq(undefined)
    });


    it('should generate with single numeric question and empty value', async function () {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...createUtr(new ObjectId(), 'aa', { valueType: UtrValueType.Number }),
        utrvs: [createReportUtrv({ value: undefined, valueData: {} })],
      };
      const { records: [firstRow] } = await cr.convertData(createConvertData([rowData]));
      expect(firstRow[initiativeFirstIndex]).eq('NA')
    });

    it('should generate numeric question without valueData, still NA', async function () {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...createUtr(new ObjectId(), 'aa', { valueType: UtrValueType.Number }),
        utrvs: [createReportUtrv({ value: undefined })],
      };
      const { records: [firstRow] } = await cr.convertData(createConvertData([rowData]));
      expect(firstRow[initiativeFirstIndex]).eq('NA')
    });

    it('should generate numeric question with explicit notApplicableType', async function () {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...createUtr(new ObjectId(), 'aa', { valueType: UtrValueType.Number }),
        utrvs: [createReportUtrv({ valueData: { notApplicableType: NotApplicableTypes.NR } })],
      };
      const { records: [firstRow] } = await cr.convertData(createConvertData([rowData]));
      expect(firstRow[initiativeFirstIndex]).eq('NR')
    });



    it('should generate with multi row table question', async function () {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...utrTableOne,
        maxRows: 2,
        utrvs: [
          {
            ...utrTableOneUtrv,
            surveyId,
            effectiveDate: surveyFilterOne.effectiveDate,
            surveyPeriod: surveyFilterOne.period,
            surveyType: surveyFilterOne.type,
          },
          createReportUtrv({
            initiativeId: secondInitiativeId,
            valueData: {
              table: [
                [
                  { code: 'number_col2', value: 2002 },
                  { code: 'valueList_col3', value: valueListTestTable.options[0].code, },
                ],
              ]
            }
          }),
        ],
      };
      const { records } = await cr.convertData(createConvertData([rowData]));

      let rowIndex = 0;
      utrTableOneUtrv.valueData?.table?.forEach((row) => {
        utrTableOne.valueValidation?.table?.columns.forEach((c, index) => {
          const recordValue = records[rowIndex][initiativeFirstIndex];
          const value = row[index].value;
          const expectedValue = isListColumn(c) ? resolveListValues(value, valueListTestTable.options) : value;
          expect(recordValue, `Record ${rowIndex}`).eq(expectedValue)
          rowIndex++;
        })
      })
    });


    it('should add currency text prefix and numberScale', async function () {
      const [firstCol] = utrTableTwo.valueValidation?.table?.columns ?? [];
      const firstColValue = 20;
      const tableData = [
        [{ code: firstCol.code, value: firstColValue }],
        [{ code: firstCol.code, value: '' }, { code: '2', value: 1}],
        [{ code: firstCol.code, value: undefined }, { code: '2', value: 3}],
        [{ code: firstCol.code, value: 0 }, { code: '2', value: 3}],
      ];
      const rowData: CustomReportData = {
        unitConfig: undefined,
        ...utrTableTwo,
        maxRows: tableData.length,
        utrvs: [
          createReportUtrv({
            initiativeId: initiativeOneSimple._id,
            valueData: {
              table: tableData,
            },
          }),
        ],
      };
      const { columns, records: [firstRow, second, third, four] } = await cr.convertData(createConvertData([rowData]));

      // Unit column is blank for currency
      const unitIndex = columns.findIndex(c => c.id === 'unit');
      expect(firstRow[unitIndex]).eq('');

      const numberScaleIndex = columns.findIndex(c => c.id === 'numberScale');
      expect(firstRow[numberScaleIndex]).eq(firstCol.numberScale);

      const recordValue = firstRow[initiativeFirstIndex];
      // "USD 20"
      expect(recordValue).eq(`${firstCol.unit} ${firstColValue}`)

      // Ensure undefined is not written or string value
      expect(second[initiativeFirstIndex]).eq(tableData[1][0].value)
      expect(third[initiativeFirstIndex]).eq(tableData[2][0].value)

      // Value with 0 should still work
      expect(four[initiativeFirstIndex]).eq(`${firstCol.unit} ${tableData[3][0].value}`)
    });

    it('should generate report with Tag column', async () => {
      const utrIds = Array.from({ length: 3 }).map(() => new ObjectId());
      const [utrIdOne, utrIdTwo, utrIdThree] = utrIds;
      const utrTagMap = new Map([
        [utrIdOne.toString(), []],
        [utrIdTwo.toString(), ['tag1']],
        [utrIdThree.toString(), ['tag1', 'tag2']],
      ]);

      const expectedTagResult: { [key: string]: string } = {
        [utrIdOne.toString()]: '',
        [utrIdTwo.toString()]: 'tag1',
        [utrIdThree.toString()]: 'tag1, tag2',
      };

      const data: CustomReportData[] = utrIds.map((utrId) => ({
        ...createUtr(utrId, `code/${utrId.toString()}`, {
          valueType: UtrValueType.Number,
          type: 'gri',
          typeCode: `code/${utrId.toString()}`,
        }),
        utrvs: [createReportUtrv({ value: 12.355 })],
        unitConfig: undefined,
        utrTagMap,
      }));

      const { columns, records } = await cr.convertData({ ...createConvertData(data), utrTagMap });

      records.map((row) => {
        const codeIndex = columns.findIndex((h) => h.id === 'code');
        const utrId = data.find(utr => utr.code === row[codeIndex])?._id.toString() || '';
        const tagIndex = columns.findIndex((h) => h.id === 'tag');
        expect(row[tagIndex]).eq(expectedTagResult[utrId]);
      });
    });

    it('group by columns should be respected on subsidiary report', async () => {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        maxRows: 2,
        ...utrTableOneWithOneGroupBy,
        utrvs: [
          createReportUtrv( {
            initiativeId: secondInitiativeId,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Dog' },
                  { code: 'number_col2', value: 1 },
                ],
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'number_col2', value: 10 },
                ],
              ]
            }
          }),
          createReportUtrv( {
            initiativeId: thirdInitiativeId,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'number_col2', value: 20 },
                ],
                [
                  { code: 'text_col1', value: 'Mouse' },
                  { code: 'number_col2', value: 100 },
                ],
              ]
            }
          }),
        ],
      };
      const { records } = await cr.convertData(createConvertData([rowData]));

      const expectedOutput = [
        ['Text Col 1', 'Row 1 answer', '', '', '', undefined, 'Dog', undefined],
        ['Number Col 2', 'Row 1 answer', '', '', '', undefined, 1, undefined],
        ['Text Col 1', 'Row 2 answer', '', '', '', undefined, 'Cat', 'Cat'],
        ['Number Col 2', 'Row 2 answer', '', '', '', undefined, 10, 20],
        ['Text Col 1', 'Row 3 answer', '', '', '', undefined, undefined, 'Mouse'],
        ['Number Col 2', 'Row 3 answer', '', '', '', undefined, undefined, 100],
      ];

      const actualOutput = records.map((row) => row.slice(6));

      expectedOutput.forEach((expectedRow, rx) => {
        expectedRow.forEach((expectedCell, cx) => {
          expect(actualOutput?.[rx]?.[cx]).eq(expectedCell, 'Mismatch at Row ' + (rx + 1) + ' Col ' + (cx + 1));
        });
      });
    })

    it('should generate data with multi initiatives and multi surveys selected', async () => {
      const surveyFilters = [surveyFilterOne, surveyFilterTwo];
      const initiatives = [
        { ...initiativeOneSimple, matchedSurveyFilters: [surveyFilterOne, surveyFilterTwo] },
        { _id: secondInitiativeId, name: 'second', matchedSurveyFilters: [surveyFilterOne, surveyFilterTwo] },
        { _id: thirdInitiativeId, name: 'third', matchedSurveyFilters: [surveyFilterTwo] },
      ];
      const rowData: CustomReportData = {
        ...createUtr(new ObjectId(), 'aa', {
          valueType: UtrValueType.Text,
          type: 'gri',
        }),
        utrvs: [
          createReportUtrv({
            initiativeId: initiatives[0]._id,
            valueData: { data: 'Text 1-1 answer' },
            effectiveDate: surveyFilterOne.effectiveDate,
            surveyPeriod: surveyFilterOne.period,
            surveyType: surveyFilterOne.type,
          }),
          createReportUtrv({
            initiativeId: initiatives[0]._id,
            valueData: { data: 'Text 1-2 answer' },
            effectiveDate: surveyFilterTwo.effectiveDate,
            surveyPeriod: surveyFilterTwo.period,
            surveyType: surveyFilterTwo.type,
          }),
          createReportUtrv({
            initiativeId: initiatives[1]._id,
            valueData: { data: 'Text 2-1 answer' },
            effectiveDate: surveyFilterOne.effectiveDate,
            surveyPeriod: surveyFilterOne.period,
            surveyType: surveyFilterOne.type,
          }),
          createReportUtrv({
            initiativeId: initiatives[1]._id,
            valueData: { data: 'Text 2-2 answer' },
            effectiveDate: surveyFilterTwo.effectiveDate,
            surveyPeriod: surveyFilterTwo.period,
            surveyType: surveyFilterTwo.type,
          }),
          // no utrv available for initiatives[2] with surveyFilterOne
          createReportUtrv({
            initiativeId: initiatives[2]._id,
            valueData: { data: undefined },
            effectiveDate: surveyFilterTwo.effectiveDate,
            surveyPeriod: surveyFilterTwo.period,
            surveyType: surveyFilterTwo.type,
          }),
        ],
        unitConfig: undefined,
        alternatives: {
          cdp_water: { name: 'cdp 1', valueLabel: 'cdp label' },
          blabs: { name: 'b labs', valueLabel: ' blabs label' },
          futurefit: { name: 'future fit', valueLabel: ' random futurefit' },
        },
      };
      const { headers, records } = await cr.convertData(createConvertData([rowData], { surveyFilters, initiatives }));
      const expectedHeaders = [
        'Reporting module',
        'Code',
        'Mapping',
        `${QUESTION.CAPITALIZED_SINGULAR} Type`,
        `${QUESTION.CAPITALIZED_SINGULAR} Title`,
        QUESTION.CAPITALIZED_SINGULAR,
        `Sub-${QUESTION.SINGULAR}`,
        'Row',
        'Unit',
        'Number Scale',
        'Tag',
        ...initiatives
          .map((initiative) =>
            initiative.matchedSurveyFilters.map((surveyFilter) =>
              getInitiativeReportHeader({ name: initiative.name, surveyIncluded: true, ...surveyFilter })
            )
          )
          .flat(),
      ];

      expect(headers).to.deep.eq(expectedHeaders);

      const expectedRecords = [
        [
          'GRI 2019',
          '',
          'cdp_water , B-Labs , FF ',
          'text',
          '',
          '',
          undefined,
          '',
          '',
          '',
          '',
          'Text 1-1 answer',
          'Text 1-2 answer',
          'Text 2-1 answer',
          'Text 2-2 answer',
          'NA',
        ],
      ];
      expect(records).to.deep.eq(expectedRecords);
    });
  });

  describe('convertSurveyData fn', function () {
    const utrTagMap = new Map<string, string[]>();
    const secondInitiativeId = new ObjectId();
    const thirdInitiativeId = new ObjectId();

    const surveys = [
      surveyOne,
      { ...surveyOne, _id: secondInitiativeId },
    ]
    const valueLists = allValueLists;

    const createConvertSurveyData = (data: CustomReportData[] = []) => ({
      data,
      surveys,
      valueLists: valueLists,
      utrTagMap,
    });

    it('group by columns should be respected on data comparison report', async () => {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        maxRows: 2,
        ...utrTableOneWithOneGroupBy,
        utrvs: [
          createReportUtrv( {
            initiativeId: secondInitiativeId,
            surveyId: surveys[0]._id,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Dog' },
                  { code: 'number_col2', value: 1 },
                ],
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'number_col2', value: 10 },
                ],
              ]
            }
          }),
          createReportUtrv( {
            initiativeId: thirdInitiativeId,
            surveyId: surveys[1]._id,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'number_col2', value: 20 },
                ],
                [
                  { code: 'text_col1', value: 'Mouse' },
                  { code: 'number_col2', value: 100 },
                ],
              ]
            }
          }),
        ],
      };
      const { records } = await cr.convertSurveyData(createConvertSurveyData([rowData]));

      const expectedOutput = [
        ['Text Col 1', 'Row 1 answer', '', '', '', 'Dog', undefined],
        ['Number Col 2', 'Row 1 answer', '', '', '', 1, undefined],
        ['Text Col 1', 'Row 2 answer', '', '', '', 'Cat', 'Cat'],
        ['Number Col 2', 'Row 2 answer', '', '', '', 10, 20],
        ['Text Col 1', 'Row 3 answer', '', '', '', undefined, 'Mouse'],
        ['Number Col 2', 'Row 3 answer', '', '', '', undefined, 100],
      ];

      const actualOutput = records.map((row) => row.slice(6));

      expectedOutput.forEach((expectedRow, rx) => {
        expectedRow.forEach((expectedCell, cx) => {
          expect(actualOutput?.[rx]?.[cx]).eq(expectedCell, 'Mismatch at Row ' + (rx + 1) + ' Col ' + (cx + 1));
        });
      });
    })

    it('group by columns should be respected on data comparison report - multi-column group', async () => {
      const rowData: CustomReportData = {
        unitConfig: undefined,
        maxRows: 2,
        ...utrTableOneWithTwoGroupBy,
        utrvs: [
          createReportUtrv( {
            initiativeId: secondInitiativeId,
            surveyId: surveys[0]._id,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Dog' },
                  { code: 'text_col2', value: 'Green' },
                  { code: 'number_col2', value: 1 },
                ],
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'text_col2', value: 'Green' },
                  { code: 'number_col2', value: 10 },
                ],
                [
                  { code: 'text_col1', value: 'Dog' },
                  { code: 'text_col2', value: 'Blue' },
                  { code: 'number_col2', value: 100 },
                ],
              ]
            }
          }),
          createReportUtrv( {
            initiativeId: thirdInitiativeId,
            surveyId: surveys[1]._id,
            valueData: {
              table: [
                [
                  { code: 'text_col1', value: 'Dog' },
                  { code: 'text_col2', value: 'Blue' },
                  { code: 'number_col2', value: 20 },
                ],
                [
                  { code: 'text_col1', value: 'Cat' },
                  { code: 'text_col2', value: 'Green' },
                  { code: 'number_col2', value: 20 },
                ],
                [
                  { code: 'text_col1', value: 'Mouse' },
                  { code: 'number_col2', value: 100 },
                ],
              ]
            }
          }),
        ],
      };
      const { records } = await cr.convertSurveyData(createConvertSurveyData([rowData]));

      const expectedOutput = [
        ['Text Col 1', 'Row 1 answer', '', '', '', 'Dog', undefined],
        ['Text Col 2', 'Row 1 answer', '', '', '', 'Green', undefined],
        ['Number Col 2', 'Row 1 answer', '', '', '', 1, undefined],
        ['Text Col 1', 'Row 2 answer', '', '', '', 'Cat', 'Cat'],
        ['Text Col 2', 'Row 2 answer', '', '', '', 'Green', 'Green'],
        ['Number Col 2', 'Row 2 answer', '', '', '', 10, 20],
        ['Text Col 1', 'Row 3 answer', '', '', '', 'Dog', 'Dog'],
        ['Text Col 2', 'Row 3 answer', '', '', '', 'Blue', 'Blue'],
        ['Number Col 2', 'Row 3 answer', '', '', '', 100, 20],
        ['Text Col 1', 'Row 4 answer', '', '', '', undefined, 'Mouse'],
        ['Text Col 2', 'Row 4 answer', '', '', '', undefined, undefined],
        ['Number Col 2', 'Row 4 answer', '', '', '', undefined, 100],
      ];

      const actualOutput = records.map((row) => row.slice(6));

      expectedOutput.forEach((expectedRow, rx) => {
        expectedRow.forEach((expectedCell, cx) => {
          expect(actualOutput?.[rx]?.[cx]).eq(expectedCell, 'Mismatch at Row ' + (rx + 1) + ' Col ' + (cx + 1));
        });
      });
    })
  });
});
