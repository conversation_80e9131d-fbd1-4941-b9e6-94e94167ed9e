/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { createUserManager, getUserManager } from "../../../server/service/user/UserManager";
import { getOnboardingManager } from "../../../server/service/onboarding/OnboardingManager";
import { createOnboardingRepository } from "../../../server/repository/OnboardingRepository";
import { UserProvisioning } from "../../../server/service/user/UserProvisioning";
import { testLogger } from "../../factories/logger";
import { userOne } from "../../fixtures/userFixtures";
import User from "../../../server/models/user";
import { createSandbox } from "sinon";
import { expect } from "chai";
import { IdentityProvider } from "@okta/okta-sdk-nodejs";

describe('User Provisioning', () => {

  const sandbox = createSandbox();

  const userManager = createUserManager({} as any);
  const onboardingManager = getOnboardingManager();
  const onboardingRepo = createOnboardingRepository();
  const service = new UserProvisioning(
    userManager,
    onboardingManager,
    onboardingRepo,
    testLogger,
  );

  const oktaUserId = 'new-okta-test-user-id'

  const user = new User({
    userOne,
    oktaUserId
  })
  const staticIdpId = 'idpOne';


  const getIdp = (assignments: string[] = []) => ({
    policy: {
      provisioning: {
        groups: {
          action: 'ASSIGN',
          assignments,
          filter: [],
          sourceAttributeName: '',
        }
      }
    }
  } as unknown as IdentityProvider);

  describe('applyNewUserProvisioning', () => {

    beforeEach(() => {
      sandbox.stub(user, 'save').resolves(user)
      sandbox.stub(userManager, 'provisionExistingUser').resolves(user)
      sandbox.stub(onboardingRepo, 'find').resolves([])
    })

    afterEach(() => {
      sandbox.restore()
    })

    it('should do nothing on empty', async () => {
      sandbox.stub(userManager, 'getExternalIdpIds').resolves([]);
      const u = await service.applyNewUserProvisioning(user);
      expect(u.permissions).to.be.lengthOf(0);
      expect(u.oktaUserId).eq(oktaUserId);
    });

    it('should do nothing on single id with no assignments', async () => {
      sandbox.stub(userManager, 'getExternalIdpIds').resolves([staticIdpId]);
      sandbox.stub(userManager, 'getIdp').resolves(getIdp([]));
      const getOktaUser = sandbox.stub(userManager, 'getOktaUser');

      const u = await service.applyNewUserProvisioning(user);

      expect(getOktaUser.calledOnce).eq(false, 'No assignments, there should not be called');
      expect(u.oktaUserId).eq(oktaUserId);
    });

    it('should add groups on single id with assignments', async () => {
      const assignments = ['random-group-id']
      const oktaUser = {
        addToGroup: async (groupId: string) => groupId
      } as any

      sandbox.stub(userManager, 'getExternalIdpIds').resolves([staticIdpId]);
      sandbox.stub(userManager, 'getIdp').resolves(getIdp(assignments));

      const addToGroupSpy = sandbox.spy(oktaUser, 'addToGroup');
      sandbox.stub(userManager, 'getOktaUser').resolves(oktaUser);

      const u = await service.applyNewUserProvisioning(user);
      expect(u.oktaUserId).eq(oktaUserId);
      expect(addToGroupSpy.calledOnceWith([assignments]))
    });
  });
});
