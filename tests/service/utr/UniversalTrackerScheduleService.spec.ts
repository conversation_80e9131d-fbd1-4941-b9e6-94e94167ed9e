/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as sinon from 'sinon';
import * as chai from 'chai';
import { utrScheduleOne, utrScheduleReal, neverEndingSchedule } from '../../fixtures/universalTrackerScheduleFixtures';
import UniversalTrackerScheduleFactory from '../../../server/service/utr/UniversalTrackerScheduleFactory';
import moment = require('moment');
import { UniversalTrackerSchedulePlain } from '../../../server/models/universalTrackerSchedule';
import UniversalTrackerManager from '../../../server/service/utr/UniversalTrackerValueManager';
import UniversalTrackerSchedule from '../../../server/models/universalTrackerSchedule';

const newUtrId = '5bf566bd474f43459a7b2901';
const cloneObject = (obj: any) => {
  return UniversalTrackerSchedule.hydrate(JSON.parse(JSON.stringify(obj))).toObject<UniversalTrackerSchedulePlain>();
};

describe('UniversalTrackerScheduleService', () => {

  const sandbox = sinon.createSandbox();
  const utrService = UniversalTrackerScheduleFactory();
  const defaultResult = {count: 0, success: true, failCount: 0, successCount: 0};
  let manager: sinon.SinonStub;
  let trackerSchedule: sinon.SinonStub;

  before(() => {
    manager = sandbox.stub(UniversalTrackerManager as any, 'create')
      .callsFake(() => Promise.resolve({_id: newUtrId}));

    trackerSchedule = sandbox.stub(UniversalTrackerSchedule as any, 'findByIdAndUpdate')
      .callsFake((id: any, update: any) => Promise.resolve(update));
  });
  after(() => sandbox.restore());
  beforeEach(() => trackerSchedule.resetHistory());

  it('process - create tracker values', async () => {
    const utrv = cloneObject(utrScheduleOne);
    utrv.nextRunDate = moment().endOf('day').toDate();
    const expectedResult = {...defaultResult, count: 1, successCount: 1};

    const result = await utrService.process([utrv]);
    chai.expect(result).to.be.deep.equal(expectedResult);
    chai.expect(manager.calledOnce);
    chai.expect(trackerSchedule.calledOnce);
  });

  it('process - create - overdue', async () => {
    const utrv: UniversalTrackerSchedulePlain = cloneObject(utrScheduleOne);
    utrv.nextRunDate = moment().subtract('1', 'days').hours(5).toDate();
    utrv.cronSchedule = '0 7 * * *';

    const expectedResult = {...defaultResult, count: 1, successCount: 2};

    const result = await utrService.process([utrv]);
    chai.expect(result).to.be.deep.equal(expectedResult);
    chai.expect(manager.calledTwice);
    chai.expect(trackerSchedule.calledOnce);

    const nextRunDate: Date = trackerSchedule.args[0][1].nextRunDate;

    const next = moment(nextRunDate);
    const isBefore = next.isBefore(utrv.endDate);
    chai.expect(isBefore);

    const startOfTheDay = moment().startOf('day');
    const isMatching = next.isSame(startOfTheDay);
    chai.expect(isMatching);

    const history = utrv.history;
    chai.expect(history.length).to.be.equal(2);
  });

  it('process - create - no more runs', async () => {
    const utrvSchedule: UniversalTrackerSchedulePlain = cloneObject(utrScheduleOne);

    utrvSchedule.nextRunDate = moment(utrvSchedule.endDate)
      .subtract(1, 'h').toDate();
    await utrService.process([utrvSchedule]);

    const nextRunDate: Date = trackerSchedule.args[0][1].nextRunDate;
    const next = moment(nextRunDate);
    const isMatching = next.isSame(utrvSchedule.endDate);
    chai.expect(isMatching);

    const history = utrvSchedule.history;
    chai.expect(history).to.be.empty;
  });

  it('process - no nextRunDate - fallback to startDate', async () => {
    const utrvSchedule: UniversalTrackerSchedulePlain = cloneObject(utrScheduleOne);

    delete(utrvSchedule.nextRunDate);
    utrvSchedule.startDate = moment().subtract('5', 'd').toDate();
    await utrService.process([utrvSchedule]);

    const nextRunDate: Date = trackerSchedule.args[0][1].nextRunDate;
    const next = moment(nextRunDate);
    const isMatching = next.isSame(utrvSchedule.endDate);
    chai.expect(isMatching);

    const history = utrvSchedule.history;
    chai.expect(history.length).to.be.equal(6);
  });

  it('After changing cronSchedule - nextRunDate reset', async () => {

    trackerSchedule.reset();

    // Resetting run date to the end of today (when changing cron schedule)
    const utrvSchedule = {
      ...utrScheduleReal,
      nextRunDate: moment().endOf('day').toDate()
    };

    const { successCount } = await utrService.process([utrvSchedule]);

    // Nothing to do yet
    chai.expect(successCount).to.be.equal(0);

    const nextRunDate: Date = trackerSchedule.args[0][1].nextRunDate;
    const next = moment(nextRunDate);
    const beforeEndDate = next.isBefore(utrvSchedule.endDate);
    chai.expect(beforeEndDate, 'Expect to be before end date').to.be.true;
  });

  it('After changing cronSchedule - nextRunDate reset yesterday', async () => {

    // Actual sample
    const day = moment().add(1, 'day').get('day');
    const utrvSchedule = {...utrScheduleReal, cronSchedule: `0 0 * * ${day}` };

    // Resetting run date to the end of today (when changing cron schedule)
    // Assuming it next day run (therefore we subtract one day)
    const resetNextRunDate = moment().subtract(1, 'd').endOf('day');
    utrvSchedule.nextRunDate = resetNextRunDate.toDate();

    const { successCount } = await utrService.process([utrvSchedule]);

    // Nothing to do yet
    chai.expect(successCount).to.be.equal(0);

    const nextRunDate: Date = trackerSchedule.args[0][1].nextRunDate;
    const next = moment(nextRunDate);
    const beforeEndDate = next.isBefore(utrvSchedule.endDate);
    chai.expect(beforeEndDate, 'Expect to be before end date').to.be.true;
  });

  it('After changing cronSchedule - nextRunDate reset yesterday - expect one', async () => {

    // Actual sample
    const day = moment().get('day');
    const utrvSchedule = {...utrScheduleReal, cronSchedule: `0 0 * * ${day}` };

    // Resetting run date to the end of today (when changing cron schedule)
    // Assuming it next day run (therefore we subtract one day)
    const resetNextRunDate = moment().subtract(1, 'd').endOf('day');
    utrvSchedule.nextRunDate = resetNextRunDate.toDate();

    const { successCount } = await utrService.process([utrvSchedule]);

    // Nothing to do yet
    chai.expect(successCount).to.be.equal(1);

    const { nextRunDate, active }: any = trackerSchedule.args[0][1];
    const next = moment(nextRunDate);
    const beforeEndDate = next.isBefore(utrvSchedule.endDate);
    chai.expect(beforeEndDate, 'Expect to be before end date').to.be.true;
    chai.expect(active);
  });

  it('expect no schedules created when next/end dates are the same', async () => {
    const utrvSchedule = cloneObject(neverEndingSchedule);
    const { successCount } = await utrService.process([utrvSchedule]);
    chai.expect(successCount).to.be.equal(0);

    const { nextRunDate, active }: any = trackerSchedule.args[0][1];
    const next = moment(nextRunDate);
    const beforeEndDate = next.isSame(utrvSchedule.endDate);
    chai.expect(beforeEndDate, 'Expect to be same as end date').to.be.true;
    chai.expect(active).to.be.false;
  });

});
