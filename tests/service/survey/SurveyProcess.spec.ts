/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import * as sinon from 'sinon';
import * as chai from 'chai';
import { expect } from 'chai';
import setup from '../../setup';
import { surveyCreateData, surveyOne } from '../../fixtures/survey';
import { userIdThree, userOne, userTwo } from '../../fixtures/userFixtures';
import UniversalTracker from '../../../server/models/universalTracker';
import { ObjectId } from 'bson';
import { SurveyProcess, UtrvProcessData } from '../../../server/service/survey/SurveyProcess';
import Survey, { SurveyModelPlain } from '../../../server/models/survey';
import {
  Blueprint,
  BlueprintRepository,
  getBlueprintRepository
} from '../../../server/repository/BlueprintRepository';
import { getCompositeUtrConfigRepository } from '../../../server/repository/CompositeUtrConfigRepository';
import {
  allTestSurveys,
  surveySourceOne,
  testSdgTargetConfig,
  testUtrConfigs,
} from '../../fixtures/survey/surveySourceFixtures';
import {
  createUtr,
  fragmentUtrOne
} from '../../fixtures/universalTrackerFixtures';
import {
  createUtrv,
  fragmentUtrvOne
} from '../../fixtures/compositeUTRVFixtures';
import { gri2019 } from '../../../server/survey/gri2019';
import { getCompositeData } from '../../../server/service/utr/utrvUtil';
import { sdg_14_1_pc_water_discharge_recycled } from '../../../server/survey/configs/gri2019/sdg_14_1_pc_water_discharge_recycled';
import { verify } from './commonchecks';
import { baseline2019 } from '../../../server/survey/configs/baseline2019/baseline2019';
import { sdg_14_1 } from '../../../server/survey/configs/baseline2019/sdg_14_1';
import { SurveyProcessRepository } from '../../../server/repository/SurveyProcessRepository';
import {
  additionalFormConfig,
  groupValueChainUtrCode,
  utrGroupValueChain
} from '../../fixtures/survey/surveySourceGriFixtures';
import { sdg_12_2 } from '../../../server/survey/configs/baseline2019/sdg_12_2';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import {
  createClonedData,
  createCombinedUtrv,
} from '../../factories/universalTrackerValue';
import { checkCompositeData, checkUseCompositeConfigs } from './checks';
import moment = require('moment');
import { generic_capex_capital_expenditure } from '../../../server/survey/configs/gri2019/generic_capex_capital_expenditure';
import { SupportedMeasureUnits } from '../../../server/service/units/unitTypes'
import { generic_pc_water_recycled } from '../../../server/survey/configs/gri2019/generic_pc_water_recycled'
import { ClonedData } from '../../../server/service/survey/model/ProcessData';
import { ActionList } from '../../../server/service/utr/constants';
import { SurveyClone } from '../../../server/service/survey/SurveyClone';
import {
  SourceTypes,
  StakeholderGroup,
} from '../../../server/models/public/universalTrackerValueType';
import { getContributionWithCode } from '../../fixtures/compositeValueCalculatorFixtures';
import {
  createStakeholderGroup
} from '../../../server/service/stakeholder/StakeholderGroupManager';
import { CompositeData } from '../../../server/models/universalTrackerValue';

describe('SurveyProcess V2', () => {
  const surveyRepo = getBlueprintRepository();

  const mockSurveyRepo = new BlueprintRepository(
    allTestSurveys,
    getCompositeUtrConfigRepository(testUtrConfigs),
  );
  const sandbox = sinon.createSandbox();

  const surveyModel = new Survey(surveyCreateData);

  const [utrCodeWithUnit] = generic_pc_water_recycled.fragmentUtrCodes ?? [];
  const expectedUnit = 'kWh'

  const regSurveyCode = 'regenerated_reference_data';
  const borrowedUtrvId = new ObjectId();
  const utr14_1_1 = createUtr(
    new ObjectId(),
    sdg_14_1_pc_water_discharge_recycled.compositeUtrCode,
    {
      unitType: SupportedMeasureUnits.energy
    }
  );
  const commonComp: any = {
    fragmentUtrvs: [],
    surveyId: surveyOne._id,
    blueprint: baseline2019.code,
  };
  const dd = createUtrv(borrowedUtrvId, utr14_1_1._id, 15, {
    compositeData: {
      ...commonComp,
      fragmentUtrvs: [],
      secondary: [{
        fragmentUtrvs: [],
        blueprint: gri2019.code,
        configCode: 'non-existent-code-get-fixed'
      }]
    },
    sourceCode: sdg_14_1.code,
  });

  const mainUtrv = createCombinedUtrv(additionalFormConfig.compositeUtrCode, {
    compositeData: { ...commonComp, fragmentUtrvs: [] },
    sourceCode: sdg_12_2.code,
    initiativeId: initiativeOneSimple._id,
  });

  const capex = createCombinedUtrv('gri/2019/capex-capital-expenditure', {
    compositeData: {
      fragmentUtrvs: [],
      surveyId: surveyOne._id,
      blueprint: baseline2019.code,
    },
    verificationRequired: false,
    evidenceRequired: false,
    initiativeId: initiativeOneSimple._id
  });

  before(() => {
    sandbox.stub(surveyModel, 'save')
      .callsFake(() => Promise.resolve(surveyModel));
    setup.wrapStub(sandbox, UniversalTracker, 'findOne', ({ code }) => {

      let toReturn: any = { _id: new ObjectId(), code };
      if (code === utrCodeWithUnit) {
        toReturn = {
          _id: new ObjectId(),
          code,
          unitType: SupportedMeasureUnits.energy,
          unit: expectedUnit,
        }
      }

      const fake: any = {
        exec: () => (toReturn)
      };
      fake.lean = () => fake;
      return fake;
    });
    const getRefData = (survey: SurveyModelPlain) => {
      if (survey.sourceName === utrGroupValueChain.code) {
        return Promise.resolve([
          { ...mainUtrv },
        ]);
      }
      if (survey.sourceName === gri2019.code) {
        return Promise.resolve([
          { ...mainUtrv },
          { ...dd, universalTracker: [utr14_1_1] },
          { ...capex },
        ]);
      }
      return Promise.resolve([]);
    };
    setup.wrapStub(sandbox, SurveyProcessRepository, 'getReferencedData', getRefData);
    setup.wrapStub(sandbox, SurveyProcessRepository, 'loadSurveyUtrvs', (survey: SurveyModelPlain) => {
      if (survey.code === regSurveyCode) {
        return Promise.resolve([
          {
            ...fragmentUtrvOne,
            value: undefined,
            status: 'created',
            universalTracker: [utr14_1_1],
          },
        ]);
      }
      const duplicateId = new ObjectId('65cda1f034fe19a5de01b9ec');
      return Promise.resolve([
        {
          ...fragmentUtrvOne,
          universalTracker: [fragmentUtrOne],
          created: moment().subtract(2, 'years').toDate().toISOString(),
        },
        {
          ...fragmentUtrvOne,
          _id: duplicateId,
          universalTracker: [fragmentUtrOne],
          created: moment().subtract(5, 'years').toDate().toISOString(),
          verificationRequired: true,
          evidenceRequired: true,
        }
      ]);
    });
  });

  after(() => sandbox.restore());

  describe('surveySourceOne blueprint', () => {

    const bc = getContributionWithCode(surveySourceOne.code, mockSurveyRepo);
    let forms: Blueprint;
    before(async () => {
      forms = await mockSurveyRepo.mustFindExpandedByCode(surveySourceOne.code);
    });

    it('test One config', async () => {
      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        surveyModel,
        bc
      );
      const modelsToSave = await surveyProcess.generateSurvey();
      expect(modelsToSave).to.be.instanceof(Map)
      verify(surveyModel, modelsToSave);
    });

    it('regenerate - duplicated utrv will move to disabled (default)', async () => {
      const survey = new Survey({
        ...surveyCreateData,
        verificationRequired: true,
        evidenceRequired: true,
      });
      const surveyProcess = new SurveyProcess(forms, userOne, survey);
      const modelsToSave = await surveyProcess.regenerateSurvey();
      verify(survey, modelsToSave);

      const [disabled] = survey.disabledUtrvs;

      chai.expect(disabled.toHexString()).to.be.equal(fragmentUtrvOne._id.toString());

      // Fragment with verification/evidence required should remain as true, last one wins.
      // No longer changed back to false, because it does not have value chain anymore
      const updatedUtrv = modelsToSave.get(fragmentUtrOne.code);

      // Second duplicate have true for both, therefore it remains as it was
      chai.expect(updatedUtrv?.verificationRequired).to.be.equal(true);
      chai.expect(updatedUtrv?.evidenceRequired).to.be.equal(true);
    });

    it('should clone data', async () => {

      const evidence = [new ObjectId(), new ObjectId()]
      const value = 100;
      const [codeOne, codeTwo, codeThree] = testSdgTargetConfig.fragmentUtrCodes ?? [];

      const createClone = (code: string, group: StakeholderGroup) => ({
        ...createClonedData({
          evidence,
          universalTracker: createUtr(new ObjectId(), code),
          value,
          status: ActionList.Verified,
          valueData: { table: [], input: { unit: '', table: [], value, numberScale: '' } },
          stakeholders: group,
        }),
        isCloned: true,
        _id: undefined,
      });
      const clonedData: ClonedData[] = [];

      clonedData.push(createClone(
        codeOne,
        createStakeholderGroup([userOne._id], [userTwo._id])
      ));

      clonedData.push(createClone(
        codeTwo,
        createStakeholderGroup([], [])
      ));

      clonedData.push(createClone(
        codeThree,
        createStakeholderGroup([], [userTwo._id, userOne._id, userIdThree])
      ));

      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        surveyModel,
      );
      const options = SurveyClone.createDuplicateOption({
        user: userOne,
        effectiveDate: new Date(),
        delegation: true,
        hasData: true,
      })

      const modelsToSave = await surveyProcess.clone(clonedData, options);
      verify(surveyModel, modelsToSave);

      for (const c of clonedData) {
        const utrv = modelsToSave.get(c.universalTracker?.code ?? '');
        if (!(utrv && 'toObject' in utrv)) {
          throw new Error(`Expected value field to be set for ${c.universalTracker?.code}`)
        }
    
        const plain = utrv.toObject();
        expect(plain.value).to.be.equal(value);
        expect(plain.valueData).to.be.eqls(c.valueData);
        expect(plain.status).to.be.eqls(ActionList.Updated);
        expect(plain.stakeholders).to.be.eqls(c.stakeholders);
        const history = plain.history;
        expect(history).to.be.lengthOf(2)
      }
    });

    it('should remove unit and numberScale when cloning with undefined values', async () => {
      const evidence = [new ObjectId()];
      const value = 100;
      const [codeOne] = testSdgTargetConfig.fragmentUtrCodes ?? [];

      const clonedData: ClonedData[] = [{
        ...createClonedData({
          evidence,
          universalTracker: createUtr(new ObjectId(), codeOne),
          value,
          status: ActionList.Verified,
          valueData: { 
            table: [], 
            input: { 
              table: [], 
              value,
              unit: undefined,
              numberScale: undefined 
            } 
          },
          stakeholders: createStakeholderGroup([userOne._id], [])
        }),
        isCloned: true,
        _id: undefined
      }];

      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        surveyModel
      );

      const options = SurveyClone.createDuplicateOption({
        user: userOne,
        effectiveDate: new Date(),
        delegation: true,
        hasData: true
      });

      const modelsToSave = await surveyProcess.clone(clonedData, options);
      verify(surveyModel, modelsToSave);

      const utrv = modelsToSave.get(codeOne);
      if (!(utrv && 'toObject' in utrv)) {
        throw new Error(`Expected value field to be set for ${codeOne}`);
      }
      
      const plain = utrv.toObject();
      const clonedUtrv = clonedData[0];
      expect(plain.value).to.be.equal(value);
      expect(plain.valueData?.table).to.be.eqls(clonedUtrv.valueData?.table);
      expect(plain.valueData?.input?.value).to.be.eqls(clonedUtrv.valueData?.input?.value);
      expect(plain.valueData?.input?.table).to.be.eqls(clonedUtrv.valueData?.input?.table);
      expect(plain.valueData?.input).to.not.have.property('unit');
      expect(plain.valueData?.input).to.not.have.property('numberScale');
      expect(plain.status).to.equal(ActionList.Updated);  
    });
  });

  describe('generate GRI2019 Survey', () => {

    it('Utrv exists different owner sourceName', async () => {
      const expectedStakeholderId = new ObjectId();
      const surveyGriModel = new Survey({
        ...surveyCreateData,
        sourceName: gri2019.code,
        verificationRequired: true,
        stakeholders: {
          stakeholder: [expectedStakeholderId],
          verifier: [],
          escalation: []
        }
      });

      const forms = await surveyRepo.getExpandedBlueprintByCode(gri2019.code) as Blueprint;
      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        surveyGriModel,
      );
      const modelsToSave = await surveyProcess.generateSurvey();
      const getUtrv = (code: string) => modelsToSave.get(code);
      // Sub Fragment
      const [fragmentCode] = sdg_14_1_pc_water_discharge_recycled.fragmentUtrCodes ?? [];

      const fragment = getUtrv(fragmentCode) as UtrvProcessData;
      const questionCode = sdg_14_1_pc_water_discharge_recycled.compositeUtrCode;

      chai.expect(fragment?.sourceCode).to.be.equal('');
      chai.expect(fragment?.sourceType).to.be.equal(SourceTypes.Fragment);

      // Referenced question
      const sdg14_1_1 = getUtrv(questionCode) as UtrvProcessData;
      chai.expect(String(sdg14_1_1._id)).to.be.equal(borrowedUtrvId.toString());

      // Source code is still owned by baseline survey, and it stays as fragment
      chai.expect(sdg14_1_1.sourceCode).to.be.equal(sdg_14_1.code);
      chai.expect(sdg14_1_1.sourceType).to.be.equal(SourceTypes.Fragment);

      // Owner composite is not updated
      const fragmentIds = sdg14_1_1.compositeData?.fragmentUtrvs.map(String);
      chai.expect(fragmentIds).not.to.contain(fragment._id.toString());

      chai.expect(String(sdg14_1_1.compositeData?.surveyId))
        .to.be.equal(surveyOne._id.toString());

      const secondaryCompData = getCompositeData(sdg14_1_1, surveyGriModel.sourceName) as CompositeData;
      const { surveyId, blueprint } = secondaryCompData;

      chai.expect(String(surveyGriModel._id)).to.be.equal(surveyId?.toString());

      // We have survey source code as well
      chai.expect(blueprint).to.be.equal(gri2019.code);

      // Borrowed Utrv should not be tracked on survey
      const ids = [
        ...surveyGriModel.compositeUtrvs,
        ...surveyGriModel.fragmentUtrvs,
        ...surveyGriModel.disabledUtrvs,
        ...surveyGriModel.subFragmentUtrvs
      ].map(String);

      chai.expect(ids).not.to.contain(sdg14_1_1._id.toString());

      const capexLinkQuestion = getUtrv(capex.universalTracker[0].code) as UtrvProcessData;
      const linkedCompData = getCompositeData(capexLinkQuestion);
      chai.expect(linkedCompData?.blueprint).to.be.equal(baseline2019.code);

      chai.assert(
        capexLinkQuestion.verificationRequired,
        'Expected verification to be required'
      );

      const stakeholder = capexLinkQuestion.stakeholders?.stakeholder.map(String);
      expect(stakeholder).to.contain(String(expectedStakeholderId));

      const main_302_4 = getUtrv('gri/2019/302-4/a') as UtrvProcessData;
      const comp_302_4 = getCompositeData(main_302_4) as CompositeData;
      chai.expect(comp_302_4.blueprint).to.be.equal(gri2019.code);

      // All value chain configs
      [
        [generic_capex_capital_expenditure, gri2019.code],
      ].forEach(([config, blueprint]: any) => {
        return checkUseCompositeConfigs(modelsToSave, config, blueprint);
      });
    });

    it('regenerate - duplicated utrv will move to disabled 2019', async () => {
      const forms = await surveyRepo.getExpandedBlueprintByCode(gri2019.code) as Blueprint;
      const survey = new Survey({
        ...surveyCreateData,
        code: regSurveyCode,
        sourceName: gri2019.code,
      });
      const surveyProcess = new SurveyProcess(forms, userOne, survey);
      const modelsToSave = await surveyProcess.regenerateSurvey();

      // Fragment with verification/evidence required should be reset to false.
      const getUtrv = (code: string) => modelsToSave.get(code);
      const updatedUtrv = getUtrv(utr14_1_1.code) as UtrvProcessData;
      expect(updatedUtrv).to.be.an('object');
      expect(updatedUtrv._id.toString()).to.be.equal(borrowedUtrvId.toHexString());

      const [disabled] = survey.disabledUtrvs;

      chai.expect(disabled.toHexString())
        .to.be.equal(fragmentUtrvOne._id.toString());
    });

  });

  describe('utrGroupValueChain blueprint', () => {


    const bc = getContributionWithCode(utrGroupValueChain.code, mockSurveyRepo);
    let forms: Blueprint;
    before(async () => {
      forms = await mockSurveyRepo.mustFindExpandedByCode(utrGroupValueChain.code);
    });


    it('generate utrGroupValueChain config', async () => {
      const blueprint = utrGroupValueChain.code;
      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        new Survey({
          ...surveyCreateData,
          code: regSurveyCode,
          sourceName: blueprint,
        }),
        bc
      );
      const modelsToSave = await surveyProcess.generateSurvey();

      const fragment = modelsToSave.get(groupValueChainUtrCode)
      chai.expect(fragment).to.be.an('object')
    });

    it('regenerate utrGroupValueChain config', async () => {
      const blueprint = utrGroupValueChain.code;
      const surveyProcess = new SurveyProcess(
        forms,
        userOne,
        new Survey({
          ...surveyCreateData,
          code: regSurveyCode,
          sourceName: blueprint,
        }),
        bc
      );
      const modelsToSave = await surveyProcess.regenerateSurvey();
      const main = modelsToSave.get(additionalFormConfig.compositeUtrCode);
      chai.expect(main).to.be.an('object')
      if (main) {
        const mainComp = getCompositeData(main);
        expect(mainComp?.blueprint).to.be.equal(additionalFormConfig.ownerSourceName);
        const secondary = getCompositeData(main, blueprint);
        expect(secondary?.blueprint).to.be.equal(blueprint);
      }

      checkCompositeData(modelsToSave, additionalFormConfig, blueprint)
    });
  })
});
