import { DataTransform, UtrData } from '../../../../server/service/survey/transfer/DataTransform';
import { expect } from 'chai';
import { ObjectId } from 'bson';
import {
  ColumnType,
  TableColumn,
  UtrValueType,
  ValueValidation,
  ValueValidationType,
} from '../../../../server/models/public/universalTrackerType';
import { countryListOne, griValueListTestOne, yesNoList } from '../../../fixtures/valueListFixtures';
import { ValueList } from '../../../../server/models/public/valueList';
import { generatedUUID } from '../../../../server/service/crypto/token';
import { createSandbox, SinonSpy } from 'sinon';
import { ValueListRepository } from '../../../../server/repository/ValueListRepository';
import { TableData } from "../../../../server/models/public/universalTrackerValueType";
import { testLogger } from '../../../factories/logger';
import { blueprintDefaultUnitConfig, SupportedMeasureUnits } from '../../../../server/service/units/unitTypes';
import UserError from '../../../../server/error/UserError';

type ColumnTestData = TableColumn & { value: unknown; values?: unknown[]; comment?: string };
describe('DataTransform - MultipleValueColumns', function () {
  const sandbox = createSandbox();

  before(() => {
    sandbox.stub(ValueListRepository, 'findByIds').resolves([countryListOne, griValueListTestOne, yesNoList]);
  });
  after(() => sandbox.restore());

  const dt = new DataTransform(testLogger);

  const createValueValidation = (list: ValueList): ValueValidation => ({
    valueList: {
      list: list.options,
      type: ValueValidationType.List,
      listId: list._id,
      custom: undefined,
    },
  });

  const createTableValidation = (cols: TableColumn[], maxRows?: number): ValueValidation => ({
    table: {
      validation: maxRows ? { maxRows } : undefined,
      columns: [...cols],
    },
  });

  const createColumn = (col: Partial<ColumnTestData>): ColumnTestData => {
    const code = col.code ?? 'col-' + generatedUUID();
    return {
      name: code,
      type: ColumnType.Text,
      code: code,
      value: undefined,
      ...col,
    };
  };

  const createUtrs = (dateProvider: Record<string, any>[]): UtrData[] =>
    dateProvider.map((u) => {
      return {
        _id: new ObjectId(),
        code: u.QuestionCode as string,
        valueLabel: u.Question,
        valueType: u.utrValueType,
        valueValidation: u.utrValueValidation,
      } as UtrData;
    });

  describe('transform fn', function () {
    it('should work with existing import columns with mapping value columns is "Value 1"', async () => {
      const data = [
        {
          QuestionCode: 'numba',
          Question: 'Number',
          ['Value 1']: 2,
          Comment: 'test number',
          utrValueType: UtrValueType.Number,
        },
        {
          QuestionCode: 'numba1',
          Question: '% question',
          ['Value 1']: 5,
          Comment: 'test %',
          utrValueType: UtrValueType.Percentage,
        },
        {
          QuestionCode: 'date-column',
          Question: 'date question',
          ['Value 1']: '2020-01-01',
          Comment: 'test date',
          utrValueType: UtrValueType.Date,
        },
        {
          QuestionCode: 'text-column',
          Question: 'text question',
          ['Value 1']: 'Great!',
          Comment: 'test Text',
          utrValueType: UtrValueType.Text,
        },
        {
          QuestionCode: 'ValueList-column',
          Question: 'ValueList question',
          ['Value 1']: 'yes',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(yesNoList),
          expectedValue: 'yes',
        },
        {
          QuestionCode: 'ValueList-column-YES',
          Question: 'ValueList question YES',
          ['Value 1']: 'YES',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(yesNoList),
          expectedValue: 'yes',
        },
        {
          QuestionCode: 'ValueList-custom-yes',
          Question: 'ValueList custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: 'gb',
        },
        {
          QuestionCode: 'ValueListMulti-custom-yes',
          Question: 'ValueListMulti custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland, us',
          Comment: 'test ValueListMulti',
          utrValueType: UtrValueType.ValueListMulti,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: ['gb', 'us'],
        },
        {
          QuestionCode: 'ValueListMulti-custom-yes-NR',
          Question: 'ValueListMulti custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland, us',
          Comment: 'test ValueListMulti NR',
          NotApplicableType: 'not_reported',
          utrValueType: UtrValueType.ValueListMulti,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: ['gb', 'us'],
        },
      ];

      const utrs = data.map((u) => {
        return {
          _id: new ObjectId(),
          code: u.QuestionCode,
          valueLabel: u.Question,
          valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
          valueValidation: u.utrValueValidation as ValueValidation,
        } as UtrData;
      });

      const result = await dt.transform(data, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(data.length);

      data.forEach((d) => {
        const r = result.find((update) => update.utrCode === d.QuestionCode);
        if (!r) {
          throw new Error(`Failed to find ${d.QuestionCode}`);
        }
        if ([UtrValueType.Number, UtrValueType.Percentage].includes(d.utrValueType)) {
          expect(d['Value 1']).eq(r.value);
        } else if ('NotApplicableType' in d) {
          expect(d.NotApplicableType).eq(r?.valueData?.notApplicableType);
        } else {
          expect(d.expectedValue ?? d['Value 1']).eqls(r.valueData?.data);
          expect(d.expectedValue ?? d['Value 1']).eqls(r.valueData?.input?.data);
        }
        expect(d.Comment).eq(r.note);
      });
    });

    it('should work with existing import columns with Question column change to Metric column', async () => {
      const data = [
        {
          MetricCode: 'numba',
          Metric: 'Number',
          ['Value 1']: 2,
          Comment: 'test number',
          utrValueType: UtrValueType.Number,
        },
        {
          MetricCode: 'numba1',
          Metric: '% question',
          ['Value 1']: 5,
          Comment: 'test %',
          utrValueType: UtrValueType.Percentage,
        },
        {
          MetricCode: 'date-column',
          Metric: 'date question',
          ['Value 1']: '2020-01-01',
          Comment: 'test date',
          utrValueType: UtrValueType.Date,
        },
        {
          MetricCode: 'text-column',
          Metric: 'text question',
          ['Value 1']: 'Great!',
          Comment: 'test Text',
          utrValueType: UtrValueType.Text,
        },
        {
          MetricCode: 'ValueList-column',
          Metric: 'ValueList question',
          ['Value 1']: 'yes',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(yesNoList),
          expectedValue: 'yes',
        },
        {
          MetricCode: 'ValueList-column-YES',
          Metric: 'ValueList question YES',
          ['Value 1']: 'YES',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(yesNoList),
          expectedValue: 'yes',
        },
        {
          MetricCode: 'ValueList-custom-yes',
          Metric: 'ValueList custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland',
          Comment: 'test ValueList',
          utrValueType: UtrValueType.ValueList,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: 'gb',
        },
        {
          MetricCode: 'ValueListMulti-custom-yes',
          Metric: 'ValueListMulti custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland, us',
          Comment: 'test ValueListMulti',
          utrValueType: UtrValueType.ValueListMulti,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: ['gb', 'us'],
        },
        {
          MetricCode: 'ValueListMulti-custom-yes-NR',
          Metric: 'ValueListMulti custom-yes',
          ['Value 1']: 'United Kingdom of Great Britain and Northern Ireland, us',
          Comment: 'test ValueListMulti NR',
          NotApplicableType: 'not_reported',
          utrValueType: UtrValueType.ValueListMulti,
          utrValueValidation: createValueValidation(countryListOne),
          expectedValue: ['gb', 'us'],
        },
      ];

      const utrs = data.map((u) => {
        return {
          _id: new ObjectId(),
          code: u.MetricCode,
          valueLabel: u.Metric,
          valueType: (u.utrValueType ?? UtrValueType.Number) as UtrValueType,
          valueValidation: u.utrValueValidation as ValueValidation,
        } as UtrData;
      });

      const result = await dt.transform(data, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(data.length);

      data.forEach((d) => {
        const r = result.find((update) => update.utrCode === d.MetricCode);
        if (!r) {
          throw new Error(`Failed to find ${d.MetricCode}`);
        }
        if ([UtrValueType.Number, UtrValueType.Percentage].includes(d.utrValueType)) {
          expect(d['Value 1']).eq(r.value);
        } else if ('NotApplicableType' in d) {
          expect(d.NotApplicableType).eq(r?.valueData?.notApplicableType);
        } else {
          expect(d.expectedValue ?? d['Value 1']).eqls(r.valueData?.data);
          expect(d.expectedValue ?? d['Value 1']).eqls(r.valueData?.input?.data);
        }
        expect(d.Comment).eq(r.note);
      });
    });

    it('should ignore importing currency and use survey currency and log error if importing currency is different', async () => {
      const data = [
        {
          MetricCode: 'currency',
          Metric: 'Currency',
          ['Value 1']: 2,
          Unit: 'SGD',
          'Number Scale': 'millions',
          utrValueType: UtrValueType.Number,
          unitType: SupportedMeasureUnits.currency,
        },
      ];

      const errorSpy: SinonSpy = sandbox.spy(testLogger, 'error');

      const utrs = [
        {
          _id: new ObjectId(),
          code: data[0].MetricCode,
          valueLabel: data[0].Metric,
          valueType: data[0].utrValueType,
          unitType: data[0].unitType,
          unit: 'USD', // utr default currency
        } as UtrData,
      ];

      const result = await dt.transform(data, {
        utrs,
        survey: { unitConfig: { ...blueprintDefaultUnitConfig, currency: 'HKD' } },
      });

      const callArg = errorSpy.getCall(0).args[0];
      expect(callArg).to.be.instanceOf(UserError);
      expect(callArg.message).to.equal('Conflicted currency import detected');
      expect(callArg.context).to.deep.equals({
        surveyCurrency: 'HKD',
        importingCurrency: 'SGD',
      });
      expect(result[0].unit).to.eq('USD');
      expect(result[0].valueData?.input?.unit).to.eq('HKD');
    });
  });

  describe('transform table', function () {
    type DataTableProvider = ReturnType<typeof createRow>;

    const createValueColumns = (values: any[]) =>
      values.reduce((prev, cur, index) => {
        if (cur === undefined) {
          return prev;
        }
        return ({ ...prev, [`Value ${index + 1}`]: cur });
      }, {});
    const createRow = (columns: ColumnTestData[], code?: string, maxRows?: number) => {
      const questionCode = code ?? `${UtrValueType.Table}-${generatedUUID()}`;
      return {
        QuestionCode: questionCode,
        Question: questionCode,
        utrValueType: UtrValueType.Table,
        utrValueValidation: createTableValidation(columns, maxRows),
        rows: columns.map((o) => ({
          OptionCode: o.code,
          ...createValueColumns(o.values ? o.values : [o.value]),
          Comment: o.comment,
          Unit: o.unit,
        })),
      };
    };

    const convertDataProviderToMap = (dataProvider: DataTableProvider[]) => {
      const map = new Map<string, DataTableProvider>();
      dataProvider.forEach((utrRow) => {
        const mapEntry = map.get(utrRow.QuestionCode);
        if (mapEntry) {
          mapEntry.rows = mapEntry.rows.concat(utrRow.rows);
        } else {
          map.set(utrRow.QuestionCode, utrRow);
        }
      });
      return map;
    };

    const reduceToRowData = (dataProvider: DataTableProvider[]) =>
      dataProvider.reduce((acc, utrData) => {
        const { rows, Question, QuestionCode } = utrData;
        rows.forEach((r) => {
          acc.push({ Question, QuestionCode, ...r });
        });
        return acc;
      }, <unknown[]>[]);

    const getExpectedInputTable = (table: unknown[][] = []) => {
      return table.map(item => {
        return item.map((v: any) => ({ ...v, unit: undefined, numberScale: undefined }));
      })
    }

    it('should deal with empty tables', async function () {
      const dateProvider: DataTableProvider[] = [
        createRow(
          [
            createColumn({ value: '' }),
            createColumn({ type: ColumnType.Number }),
            createColumn({ type: ColumnType.Number }),
            createColumn({ value: '' }),
          ],
          'single-row-table',
          1
        ),
        createRow(
          [
            createColumn({ code: 'n1', values: ['', '        '] }),
            createColumn({ code: 'n2', type: ColumnType.Number, values: [undefined, ''] }),
            createColumn({ code: 'n3', type: ColumnType.Number, values: [undefined, ''] }),
            createColumn({ code: 'n4', type: ColumnType.Number, values: [undefined, ''] }),
          ],
          'multi-row-utr'
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(0);
    });

    it('should validate value for column with options', async function () {
      const [firstOption] = countryListOne.options;
      const dateProvider: DataTableProvider[] = [
        createRow(
          [
            createColumn({ value: firstOption.code, listId: countryListOne._id }),
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: '', listId: countryListOne._id }),
            createColumn({ value: 'invalid-code', listId: countryListOne._id }),
          ],
          'single-row-table',
          1
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(1);
      const valueData = result[0]?.valueData;
      expect(valueData?.table?.[0]).lengthOf(2);
      expect(valueData?.input?.table?.[0]).lengthOf(2);
    });

    it('should validate value for column with custom options if allowCustomOptions is true', async function () {
      const [firstOption] = countryListOne.options;
      const dateProvider: DataTableProvider[] = [
        createRow(
          [
            createColumn({ value: firstOption.code, listId: countryListOne._id }),
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: '', listId: countryListOne._id }),
            createColumn({ value: 'test-option', listId: countryListOne._id, validation: { allowCustomOptions: true } }),
          ],
          'single-row-table',
          1
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(1);
      const valueData = result[0].valueData;
      const firstRowTable = valueData?.table?.[0];
      const firstRowInputTable = valueData?.input?.table?.[0];

      expect(firstRowTable).lengthOf(3);
      expect(firstRowInputTable).lengthOf(3);

      expect(firstRowTable?.[2].value).to.be.eq('test-option');
      expect(firstRowInputTable?.[2].value).to.be.eq('test-option');
    });

    it('should validate value for column with options values with case insensitive way', async function () {
      const [firstOption] = countryListOne.options;
      const dateProvider: DataTableProvider[] = [
        createRow(
          [
            // This should fail
            createColumn({ value: firstOption.code.toUpperCase(), listId: countryListOne._id }),
            // this should pass
            createColumn({ value: firstOption.name, listId: countryListOne._id }),
            createColumn({ value: firstOption.name.toUpperCase(), listId: countryListOne._id }),
            createColumn({ value: firstOption.name.toLowerCase(), listId: countryListOne._id }),
          ],
          'single-row-table',
          1
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(1);
      const firstRow = result[0]?.valueData?.table?.[0] ?? [];
      expect(firstRow).lengthOf(3);
    });

    it('should skip value for column with options but numeric value', async function () {
      const [firstOption] = countryListOne.options;
      const dateProvider: DataTableProvider[] = [
        createRow(
          [
            createColumn({ value: firstOption.code, listId: countryListOne._id }),
            createColumn({ value: 123, listId: countryListOne._id }),
          ],
          'single-row-table',
          1
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(1);
      const firstRow = result[0]?.valueData?.table?.[0] ?? [];
      expect(firstRow).lengthOf(1);
    });

    it('support array of values with options', async function () {
      const [firstOption, secondOption] = countryListOne.options;
      const dateProvider: DataTableProvider[] = [
        createRow(
          [createColumn({ value: `${firstOption.code}, ${secondOption.code}`, listId: countryListOne._id })],
          'single-row-table',
          1
        ),
      ];
      const utrs = createUtrs(dateProvider);
      const rowData = reduceToRowData(dateProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      expect(result).to.be.lengthOf(1);
      const firstRow = result[0]?.valueData?.table?.[0] ?? [];
      expect(firstRow).lengthOf(1);
      expect(firstRow[0].value).lengthOf(2);
    });

    it('should transform table questions', async function () {
      const dataProvider: { row: DataTableProvider, expected: { table: unknown[][], note?: string } }[] = [
        {
          row: createRow(
            [
              createColumn({ code: 't1', value: 'yes' }),
              createColumn({ code: 't2', type: ColumnType.Number, value: 50 }),
              createColumn({ code: 't3', type: ColumnType.Number, value: 10 }),
              createColumn({ code: 't4', value: 'no' }),
            ],
            'single-row-table',
            1
          ),
          expected: {
            table: [
              [
                { code: 't1', value: 'yes' },
                { code: 't2', value: 50 },
                { code: 't3', value: 10 },
                { code: 't4', value: 'no' },
              ]
            ],
          },
        },
        {
          row: createRow(
            [
              createColumn({ code: 'n1', values: ['yes', 'no'] }),
              createColumn({ code: 'n2', type: ColumnType.Number, values: [10, 10] }),
              createColumn({ code: 'n3', type: ColumnType.Number, values: [10, undefined] }),
              createColumn({ code: 'n4', values: ['no', 'no'] }),
            ],
            'multi-row-utr'
          ),
          expected: {
            table: [
              [
                { code: 'n1', value: 'yes' },
                { code: 'n2', value: 10 },
                { code: 'n3', value: 10 },
                { code: 'n4', value: 'no' },
              ],
              [
                { code: 'n1', value: 'no' },
                { code: 'n2', value: 10 },
                { code: 'n3', value: undefined }, // Set because n3 is available in first row
                { code: 'n4', value: 'no' },
              ]
            ],
          },
        },
        {
          row: createRow(
            [
              createColumn({ code: 'n1', values: ['yes', 'no'] }),
              createColumn({ code: 'n2', type: ColumnType.Number, values: [10, 10] }),
              createColumn({ code: 'n3', type: ColumnType.Number, values: [undefined, 10] }),
              createColumn({ code: 'n4', values: ['no', 'no'] }),
            ],
            'multi-row-utr-two'
          ),
          expected: {
            table: [
              [
                { code: 'n1', value: 'yes' },
                { code: 'n2', value: 10 },
                { code: 'n3', value: undefined }, // Set because n3 is available in 2nd row
                { code: 'n4', value: 'no' },
              ],
              [
                { code: 'n1', value: 'no' },
                { code: 'n2', value: 10 },
                { code: 'n3', value: 10 },
                { code: 'n4', value: 'no' },
              ]
            ],
          },
        }
      ];

      const rowProvider = dataProvider.map(data => data.row);
      const utrs = createUtrs(rowProvider);
      const rowData = reduceToRowData(rowProvider);
      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });

      dataProvider.forEach(({ row: d, expected: { table, note } }) => {
        const r = result.find((update) => update.utrCode === d.QuestionCode);
        if (!r) {
          throw new Error(`Failed to find ${d.QuestionCode}`);
        }

        expect(r.valueData?.table).eqls(table);
        expect(r.valueData?.input?.table).eqls(getExpectedInputTable(table));
        expect(note).eq(r.note);
      });
    });

    it('should transform table questions with calculation columns', async function () {
      const dataProvider: DataTableProvider[] = [
        createRow(
          [
            createColumn({ code: 'n1', values: ['yes'] }),
            createColumn({ code: 'n2', type: ColumnType.Number, values: [50] }),
            createColumn({ code: 'n3', type: ColumnType.Number, values: [10] }),
            createColumn({
              code: 'n4',
              type: ColumnType.Number,
              calculation: {
                formula: '{n2}/{n3}',
              },
            }),
          ],
          'single-row-table',
          1
        ),
        createRow(
          [
            createColumn({ code: 'n1-value', type: ColumnType.Number, values: [50] }),
            createColumn({ code: 'n2-value', type: ColumnType.Number, values: [10] }),
            createColumn({
              code: 'n3-value',
              values: ['2'],
              type: ColumnType.Number,
              calculation: {
                formula: '{n1-value}/{n2-value}',
              },
            }),
          ],
          'single-row-table-existing-value',
          1
        ),
        createRow(
          [
            createColumn({ code: 'multi-n1', values: ['yes', 'no', 'no'] }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, values: [10, 20, 30] }),
            createColumn({ code: 'multi-n3', type: ColumnType.Number, values: [20, 50, 10] }),
            createColumn({ code: 'multi-n4', type: ColumnType.Number, values: [5, 20, 20] }),
            createColumn({ code: 'multi-n5', type: ColumnType.Number, values: [30, 100, 400] }),
            createColumn({
              code: 'multi-n6',
              type: ColumnType.Number,
              calculation: {
                formula: '{multi-n2}*{multi-n3}-{multi-n5}/{multi-n4}',
              },
              values: [undefined, undefined, undefined],
            }),
          ],
          'multi-row-utr'
        ),
        createRow(
          [
            createColumn({ code: 'multi-n1', type: ColumnType.Number, value: 20 }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 50 }),
            createColumn({
              code: 'multi-n3',
              type: ColumnType.Number,
              value: 900,
              calculation: {
                formula: '{multi-n1}*{multi-n2}',
              },
            }),
          ],
          'multi-row-calculation-with-value'
        ),
        createRow(
          [
            createColumn({ code: 'multi-n1', type: ColumnType.Number, value: '20' }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, value: 50 }),
            createColumn({
              code: 'multi-n3',
              type: ColumnType.Number,
              value: 900,
              calculation: {
                formula: '{multi-n1}*{multi-n2}',
              },
            }),
          ],
          'multi-row-calculation-with-string-value'
        ),
        createRow(
          [
            createColumn({ code: 'multi-n1', values: ['yes', undefined, undefined, 'yes'] }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, values: [10, undefined, 30, undefined] }),
            createColumn({ code: 'multi-n3', type: ColumnType.Number, values: [20, 50, undefined, undefined] }),
            createColumn({
              code: 'multi-n6',
              type: ColumnType.Number,
              calculation: {
                formula: '{multi-n2}*{multi-n3}',
              },
              values: [undefined, undefined, undefined, undefined],
            }),
          ],
          'multi-odd-rows-utr'
        ),
        createRow(
          [
            createColumn({ code: 'multi-n2', type: ColumnType.Number, values: [10, 20] }),
            createColumn({ code: 'multi-n1', values: ['yes'] }),
          ],
          'multi-odd-rows-utr-missing-rows',
        ),
        createRow(
          [
            createColumn({ code: 'multi-n1', values: ['yes'] }),
            createColumn({ code: 'multi-n2', type: ColumnType.Number, values: [10, 20] }),
          ],
          'multi-odd-rows-utr-missing-rows-first-smaller',
        ),
      ];

      const expectedProcessedData: Record<string, TableData | undefined> = {
        'single-row-table': [
          [
            { code: 'n1', value: 'yes' },
            { code: 'n2', value: 50 },
            { code: 'n3', value: 10 },
            { code: 'n4', value: 5 },
          ],
        ],
        'single-row-table-existing-value': [
          [
            { code: 'n1-value', value: 50 },
            { code: 'n2-value', value: 10 },
            { code: 'n3-value', value: 5 },
          ],
        ],
        'multi-row-utr': [
          [
            { code: 'multi-n1', value: 'yes' },
            { code: 'multi-n2', value: 10 },
            { code: 'multi-n3', value: 20 },
            { code: 'multi-n4', value: 5 },
            { code: 'multi-n5', value: 30 },
            { code: 'multi-n6', value: 194 },
          ],
          [
            { code: 'multi-n1', value: 'no' },
            { code: 'multi-n2', value: 20 },
            { code: 'multi-n3', value: 50 },
            { code: 'multi-n4', value: 20 },
            { code: 'multi-n5', value: 100 },
            { code: 'multi-n6', value: 995 },
          ],
          [
            { code: 'multi-n1', value: 'no' },
            { code: 'multi-n2', value: 30 },
            { code: 'multi-n3', value: 10 },
            { code: 'multi-n4', value: 20 },
            { code: 'multi-n5', value: 400 },
            { code: 'multi-n6', value: 280 },
          ],
        ],
        'multi-row-calculation-with-value': [
          [
            { code: 'multi-n1', value: 20 },
            { code: 'multi-n2', value: 50 },
            { code: 'multi-n3', value: 1000 },
          ],
        ],
        'multi-row-calculation-with-string-value': [
          [
            // Not actually converting strings to number, even we probably should
            { code: 'multi-n1', value: '20' },
            { code: 'multi-n2', value: 50 },
            // Calculation should still work
            { code: 'multi-n3', value: 1000 },
          ],
        ],
        'multi-odd-rows-utr': [
          [
            { code: 'multi-n1', value: 'yes' },
            { code: 'multi-n2', value: 10 },
            { code: 'multi-n3', value: 20 },
            { code: 'multi-n6', value: 200 },
          ],
          [
            { code: 'multi-n1', value: undefined },
            { code: 'multi-n2', value: undefined },
            { code: 'multi-n3', value: 50 },
            { code: 'multi-n6', value: 0 }, // Probably not what we want
          ],
          [
            { code: 'multi-n1', value: undefined },
            { code: 'multi-n2', value: 30 },
            { code: 'multi-n3', value: undefined },
            { code: 'multi-n6', value: 0 }, // Probably not what we want
          ],
          [
            { code: 'multi-n1', value: 'yes' },
            { code: 'multi-n2', value: undefined },
            { code: 'multi-n3', value: undefined },
            { code: 'multi-n6', value: '' }, // Probably not what we want
          ],
        ],
        'multi-odd-rows-utr-missing-rows': [
          [
            { code: 'multi-n2', value: 10 },
            { code: 'multi-n1', value: 'yes' },
          ],
          [
            { code: 'multi-n2', value: 20 },
            { code: 'multi-n1', value: undefined },
          ]
        ],
        'multi-odd-rows-utr-missing-rows-first-smaller': [
          [
            { code: 'multi-n1', value: 'yes' },
            { code: 'multi-n2', value: 10 },
          ],
          [
            { code: 'multi-n1', value: undefined },
            { code: 'multi-n2', value: 20 },
          ]
        ]
      };

      const expectedProcessedDataWithUnit = {
        'multi-odd-rows-utr-missing-rows': getExpectedInputTable(expectedProcessedData['multi-odd-rows-utr-missing-rows']),
        'multi-odd-rows-utr-missing-rows-first-smaller': getExpectedInputTable(expectedProcessedData['multi-odd-rows-utr-missing-rows-first-smaller'])
      }

      const utrs = createUtrs(dataProvider);
      const rowData = reduceToRowData(dataProvider);

      const result = await dt.transform(rowData, { utrs, survey: { unitConfig: blueprintDefaultUnitConfig } });
      const map = convertDataProviderToMap(dataProvider);

      Array.from(map.values()).forEach((d) => {
        const r = result.find((update) => update.utrCode === d.QuestionCode);
        if (!r) {
          throw new Error(`Failed to find ${d.QuestionCode}`);
        }

        expect(r.valueData?.table).eqls(expectedProcessedData[d.QuestionCode]);
        const k = d.QuestionCode as keyof typeof expectedProcessedDataWithUnit;
        expect(r.valueData?.input?.table).eqls(expectedProcessedDataWithUnit[k] ?? expectedProcessedData[d.QuestionCode]);
      });
    });
  });
});
