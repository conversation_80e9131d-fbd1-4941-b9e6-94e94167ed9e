/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { UpgradeResult, UpgradeService } from '../../../server/service/organization/UpgradeService';
import { initiativeStarter } from '../../fixtures/initiativeFixtures';
import { createSandbox } from 'sinon';
import { wwgLogger } from '../../../server/service/wwgLogger';
import { getCustomerManager } from '../../../server/service/payment/CustomerManager';
import { getAppConfigProvider } from '../../../server/service/app/AppConfigProvider';
import { getCurrencyService } from '../../../server/service/currency/CurrencyService';
import {
  createInvoiceLineItem,
  createPrice,
  createPriceRecurring,
  createStripeSub,
  createStripeSubItem,
  createSubWithItem,
} from '../../fixtures/subscriptions';
import { createObjectResponse, createResponse } from '../../mocks/StripeTestClient';
import Initiative, { InitiativeWithCustomer } from '../../../server/models/initiative';
import { FeatureCode } from '@g17eco/core';
import { getFeatureUsageService } from '../../../server/service/organization/FeatureUsageService';
import { BundleCodes } from '../../../server/service/payment/subscriptionCodes';
import { getUTCEndOf, toTimestamp } from '../../../server/util/date';
import moment from 'moment';
import { HydratedDocument } from 'mongoose';

describe('UpgradeService', () => {
  const customerManager = getCustomerManager();
  const featureUsageService = getFeatureUsageService();
  const upgradeService = new UpgradeService(
    wwgLogger,
    customerManager,
    getAppConfigProvider(),
    getCurrencyService(),
    featureUsageService
  );

  const userAddonProductCode = BundleCodes.UserAddon1;

  const unitAmount = 1500;
  const mockPrice = createPrice({
    currency: 'gbp',
    tiers_mode: 'volume',
    tiers: [
      {
        unit_amount: unitAmount,
        unit_amount_decimal: '1500',
        up_to: null,
        flat_amount: null,
        flat_amount_decimal: null,
      },
    ],
  });

  const basicSub = createSubWithItem([
    {
      // So happens to match with product code
      productCode: initiativeStarter.appConfigCode,
      price: {
        id: mockPrice.id,
        type: mockPrice.type,
        recurring: mockPrice.recurring ?? undefined,
      },
    },
  ]);

  describe('getCurrentDetails fn', () => {
    const sandbox = createSandbox();

    beforeEach(() => {
      sandbox.stub(featureUsageService, 'getUsage').resolves({
        featureCode: FeatureCode.Users,
        limit: 1,
        currentUsage: 1,
      });
    });

    afterEach(() => {
      sandbox.restore();
    });

    it('User addon is enabled', async () => {
      sandbox.stub(customerManager, 'getSubscriptions').resolves([basicSub]);
      sandbox.stub(customerManager, 'getPricesForProductCode').resolves(mockPrice);

      const result = await upgradeService.getCurrentDetails(initiativeStarter, FeatureCode.Users);

      expect(result.canUpgrade).to.equal(true);
      if (result.canUpgrade) {
        expect(result.productDetails).to.be.eqls({
          currency: mockPrice.currency,
          interval: 'month',
          currencySymbol: '£',
          flatAmount: 0,
          price: unitAmount / 100,
        });
      }
    });

    it('User addon is enabled with interval', async () => {
      const savedWithoutPrice = createSubWithItem([{ productCode: initiativeStarter.appConfigCode }]);

      const firstItem = createStripeSubItem({
        price: createPrice({
          ...mockPrice,
          recurring: createPriceRecurring({ interval: 'year' }),
        }),
      });

      const remoteSub = createStripeSub(
        {
          id: savedWithoutPrice.id,
          items: createResponse([firstItem]), // Explicitly cast items to SubscriptionItem[]
        },
        initiativeStarter.appConfigCode
      );

      sandbox.stub(customerManager, 'getSubscriptions').resolves([savedWithoutPrice]);
      const retrieveSubStub = sandbox
        .stub(customerManager, 'retrieveSubscription')
        .resolves(createObjectResponse(remoteSub));
      const priceFindStub = sandbox.stub(customerManager, 'getPricesForProductCode').resolves(firstItem.price);

      const result = await upgradeService.getCurrentDetails(initiativeStarter, FeatureCode.Users);

      expect(priceFindStub.calledOnceWith(BundleCodes.UserAddon1, 'year')).to.equal(true);
      expect(retrieveSubStub.calledOnceWith(savedWithoutPrice.id)).to.equal(true);

      expect(result.canUpgrade).to.equal(true);
      if (result.canUpgrade) {
        expect(result.productDetails).to.be.eqls({
          currency: mockPrice.currency,
          currencySymbol: '£',
          interval: 'year',
          flatAmount: 0,
          price: unitAmount / 100,
        });
      }
    });
  });

  describe('upgrade', () => {
    const sandbox = createSandbox();
    const initiativeWith = new Initiative(initiativeStarter) as InitiativeWithCustomer;

    beforeEach(() => {
      sandbox.stub(customerManager, 'updateSubscription');
      sandbox.stub(initiativeWith, 'save').resolves(initiativeWith);
    });
    afterEach(() => {
      sandbox.restore();
    });

    it('Add 3 User addon for existing item', async () => {
      const existingUnits = 1;
      const additionalUnits = 3;
      const expectedQuantity = existingUnits + additionalUnits;

      const mockItem = createStripeSubItem({
        id: 'si_g17eco_test_OvYBNYWkYdhRJl',
        quantity: existingUnits,
      });
      const usersSubscriptionWithAddon = createSubWithItem([
        {
          productCode: initiativeStarter.appConfigCode,
        },
        {
          id: mockItem.id,
          quantity: mockItem.quantity,
          productCode: userAddonProductCode,
        },
      ]);

      sandbox.stub(customerManager, 'getSubscriptions').resolves([usersSubscriptionWithAddon]);
      sandbox.stub(customerManager, 'getSubscriptionItem').resolves(createObjectResponse(mockItem));

      sandbox.stub(customerManager, 'updateSubscriptionItem').resolves(
        createObjectResponse({
          ...mockItem,
          subscription: usersSubscriptionWithAddon.id,
          quantity: expectedQuantity,
        })
      );

      const { details } = await upgradeService.upgrade(initiativeWith, { additionalUnits }, FeatureCode.Users);

      expect(details).to.be.eqls({
        subscriptionId: usersSubscriptionWithAddon.id,
        itemId: mockItem.id,
        quantity: expectedQuantity,
        total: 0,
      } satisfies UpgradeResult['details']);
    });

    it('Add new User addon item', async () => {
      const existingUnits = 0;
      const additionalUnits = 3;
      const expectedQuantity = existingUnits + additionalUnits;

      const mockItem = createStripeSubItem({
        id: 'si_g17eco_test_OvYBNYWkYdhRJl',
        quantity: existingUnits,
      });

      sandbox.stub(customerManager, 'getSubscriptions').resolves([basicSub]);
      sandbox.stub(customerManager, 'getPricesForProductCode').resolves(mockPrice);

      const createStub = sandbox.stub(customerManager, 'createItem').resolves(
        createObjectResponse({
          ...mockItem,
          subscription: basicSub.id,
          quantity: expectedQuantity,
        })
      );

      const { details } = await upgradeService.upgrade(initiativeWith, { additionalUnits }, FeatureCode.Users);

      expect(details).to.be.eqls({
        subscriptionId: basicSub.id,
        itemId: mockItem.id,
        quantity: expectedQuantity,
        total: 0,
      } satisfies UpgradeResult['details']);

      expect(createStub.calledOnce).eq(true);

      expect(
        createStub.calledOnceWith({
          subscription: basicSub.id,
          price: mockPrice.id,
          quantity: expectedQuantity,
          proration_behavior: 'always_invoice',
          proration_date: toTimestamp(getUTCEndOf('day')),
        })
      ).eq(true);
    });
  });

  describe('preview', () => {
    const sandbox = createSandbox();
    const initiativeWith = new Initiative(initiativeStarter) as InitiativeWithCustomer;

    beforeEach(() => {
      sandbox.stub(customerManager, 'updateSubscription');
      sandbox.stub(initiativeWith, 'save').resolves(initiativeWith);
    });

    afterEach(() => {
      sandbox.restore();
    });

    const basePreview = {
      total: 0,
      currency: 'gbp',
      period_start: moment().subtract(1, 'days').unix(),
      period_end: moment().add(7, 'days').unix(),
      lines: createResponse([]),
    };

    it('Add 3 User addon for existing item', async () => {
      const mockItem = createStripeSubItem({ id: 'si_g17eco_test_OvYBNYWkYdhRJl', quantity: 1 });
      const sub = createSubWithItem([
        { productCode: initiativeStarter.appConfigCode },
        { id: mockItem.id, quantity: mockItem.quantity, productCode: userAddonProductCode },
      ]);

      const total = 1500;
      sandbox.stub(customerManager, 'getSubscriptions').resolves([sub]);
      sandbox.stub(customerManager, 'previewInvoice').resolves({
        ...basePreview,
        lines: createResponse([
          createInvoiceLineItem({
            subscription: basicSub.id,
            currency: mockPrice.currency,
            price: createPrice({
              currency: 'gbp',
              tiers_mode: 'volume',
              tiers: [
                {
                  unit_amount: unitAmount,
                  unit_amount_decimal: '1500',
                  up_to: 1,
                  flat_amount: null,
                  flat_amount_decimal: null,
                },
                {
                  unit_amount: 1000,
                  unit_amount_decimal: '1000',
                  up_to: null,
                  flat_amount: null,
                  flat_amount_decimal: null,
                },
              ],
            }),
            amount: total,
          }),
        ]),
        total: total,
      });

      const result = await upgradeService.preview(initiativeWith, { additionalUnits: 3 }, FeatureCode.Users);

      expect(result.total).eq(total / 100);
      expect(result.status).eq('active');
      expect(result.subscriptionId).eq(sub.id);
      expect(result.lines[0]?.price).eq(10);
    });

    it('Add new User addon item', async () => {
      const total = 1500;
      sandbox.stub(customerManager, 'getSubscriptions').resolves([basicSub]);
      sandbox.stub(customerManager, 'getPricesForProductCode').resolves(mockPrice);
      sandbox.stub(customerManager, 'previewInvoice').resolves({
        ...basePreview,
        total,
      });

      const result = await upgradeService.preview(initiativeWith, { additionalUnits: 3 }, FeatureCode.Users);

      expect(result.total).eq(total / 100);
      expect(result.status).eq('active');
    });

    it('Add new User addon item while on trial', async () => {
      sandbox.stub(customerManager, 'getSubscriptions').resolves([{ ...basicSub, status: 'trialing' }]);
      sandbox.stub(customerManager, 'getPricesForProductCode').resolves(mockPrice);
      sandbox.stub(customerManager, 'previewInvoice').resolves({
        ...basePreview,
        lines: createResponse([
          createInvoiceLineItem({
            subscription: basicSub.id,
            currency: mockPrice.currency,
            price: mockPrice,
            amount: 1500,
          }),
        ]),
        total: 1500,
      });

      const result = await upgradeService.preview(
        new Initiative({
          ...initiativeStarter,
          customer: {
            id: 'fake_id',
            defaultPaymentMethod: 'fake_payment',
          },
        }) as InitiativeWithCustomer,
        { additionalUnits: 3 },
        FeatureCode.Users
      );

      expect(result.total).eq(15);
      expect(result.status).eq('trialing');

      expect(result.lines).to.be.lengthOf(1);
      const [firstLine] = result.lines;
      expect(firstLine.amount).eq(15);
      expect(firstLine.price).eq(unitAmount / 100);
      expect(firstLine.currency).eq(mockPrice.currency);
    });
  });
});
