import { expect } from 'chai';
import { ReportDocumentType } from '../../../server/models/reportDocument';
import type { CSRDFact } from '../../../server/service/reporting/types';
import { getMappingsByType } from '../../../server/service/reporting/utils';

describe('getCsrdMapping', () => {
  const existingCSRDFact: CSRDFact = 'esrs:DescriptionOfMaterialImpactsResultingFromMaterialityAssessmentExplanatory';

  it('returns default mapping when no overrides are provided', () => {
    const defaultMapping = getMappingsByType({ type: ReportDocumentType.CSRD });
    expect(defaultMapping).to.have.property(existingCSRDFact);
  });

  it('overrides existing mapping items when overrides are provided', () => {
    const overrides = {
      [existingCSRDFact]: {
        factName: existingCSRDFact,
        utrCode: 'custom-utr',
        valueListCode: 'custom-value',
      },
    };
    const newMapping = getMappingsByType({ type: ReportDocumentType.CSRD, overrides });
    expect(newMapping[existingCSRDFact]?.utrCode).to.equal('custom-utr');
    expect(newMapping[existingCSRDFact]?.valueListCode).to.equal('custom-value');
  });

  it('adds new mapping items when overrides are provided', () => {
    const overrides = {
      'esrs:NewFact': { factName: 'esrs:NewFact' as CSRDFact, utrCode: 'new-utr' },
    };
    const newMapping = getMappingsByType({ type: ReportDocumentType.CSRD, overrides });
    expect(newMapping).to.have.property('esrs:NewFact');
    expect(newMapping['esrs:NewFact']?.utrCode).to.equal('new-utr');
  });
});
